import { type RpaMessage, TASK_STATUS_PENDING } from "@repo/shared-types"
import { AMQPClient, AMQPConsumer, AMQPProducer } from "@repo/utils/amqp"
import type { ConsumeMessage } from "amqplib"
import { nanoid } from "nanoid"
import { getLogger } from "@repo/logger"

const logger = getLogger("MessageService")

export class MessageService {
	private readonly mqClient: AMQPClient
	private producers: Map<string, AMQPProducer> = new Map()
	private consumers: Map<string, AMQPConsumer> = new Map()

  constructor(mqClient: AMQPClient) {
    this.mqClient = mqClient;
  }

	async init() {
		await this.mqClient.connect()
	}

	async send(queueName: string, data: any) {
		// 检查是否已存在该队列的生产者，不存在则创建
		if (!this.producers.has(queueName)) {
			const producer = new AMQPProducer(this.mqClient, queueName)
			await producer.initialize()
			this.producers.set(queueName, producer)
		}

		const producer = this.producers.get(queueName)!

		const message: RpaMessage = {
			_client_id: nanoid(64),
			_retry_count: 0,
			_source: "scheduler",
			_timestamp: Date.now(),
			status: TASK_STATUS_PENDING,
			data: data,
		}

		await producer.send(message)
	}

	async consume(queue: string, callback: (msg: any) => void) {
		// 检查是否已存在该队列的消费者，不存在则创建
		if (!this.consumers.has(queue)) {
			const consumer = new AMQPConsumer(
				this.mqClient,
				queue,
				async (msg: ConsumeMessage | null) => {
					if (msg) {
						const message = JSON.parse(msg.content.toString()) as RpaMessage
						callback(message.data)
						// 使用消费者内部的 ackMessage 方法
						await consumer.ackMessage(msg)
					}
				},
			)

			await this.mqClient.assertQueue(queue)
			await consumer.start()
			this.consumers.set(queue, consumer)
		}
	}

	async destroy() {
		// 停止所有消费者
		for (const consumer of this.consumers.values()) {
			try {
				await consumer.stop()
			} catch (error) {
				logger.warn("Error stopping consumer during destroy:", error)
			}
		}

		// 关闭所有生产者
		for (const producer of this.producers.values()) {
			try {
				await producer.close()
			} catch (error) {
				logger.warn("Error closing producer during destroy:", error)
			}
		}

		// 清空集合
		this.consumers.clear()
		this.producers.clear()

		// 关闭主客户端连接
		try {
			await this.mqClient.close()
		} catch (error) {
			logger.warn("Error closing MQ client during destroy:", error)
		}
	}
}

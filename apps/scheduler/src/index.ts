import logger from "@repo/logger"
import { HttpServer } from "./http/server"
import {
	type BaseScheduler,
	SubmitBacklinkScheduler,
	GameTaskRecordTimeoutScheduler,
	TrendKeywordRefreshScheduler,
	DeploymentQueueScheduler,
} from "./scheduler"
import { MessageService } from "./services/MessageService.ts"
import { AMQPClient } from "@repo/utils/amqp"
import { RelatedKeywordsResultListener } from './consumer/RelatedKeywordsResultListener.ts'
import { KeywordsTrendResultListener } from './consumer/KeywordsTrendResultListener.ts'
import { TranslationTaskListener } from './consumer/TranslationTaskListener.ts'
import { PublicGameLibraryScheduler } from "./scheduler/PublicGameLibraryScheduler.ts"
const mqUrl = process.env.RABBITMQ_URL || "amqp://localhost"
const httpPort = parseInt(process.env.HTTP_PORT || "3009", 10)

// 获取当前服务名称，默认为通用服务
const serviceName = process.env.SERVICE_NAME || 'default'
logger.info(`启动Scheduler服务: ${serviceName}`)
const isFaFafa = serviceName === "scheduler-fafafa"
const isDev = process.env.NODE_ENV === "development"
async function main() {
	logger.info(`MQ URL: ${mqUrl}`)
	// 创建共享的MQ客户端实例
	const mqClient = new AMQPClient(mqUrl)
	await mqClient.connect()
	const messageService = new MessageService(mqClient)
	await messageService.init()

	// 创建调度器实例
	const schedulers: BaseScheduler[] = []

	// 外链提交调度器
	schedulers.push(new SubmitBacklinkScheduler(messageService))
	// 公共游戏库调度器
	schedulers.push(new PublicGameLibraryScheduler(messageService))
	// 游戏任务记录超时调度器
	schedulers.push(new GameTaskRecordTimeoutScheduler())
	// 非fafafa环境或开发环境启动
	if (!isFaFafa || isDev) {
		// 趋势关键词刷新调度器
		schedulers.push(new TrendKeywordRefreshScheduler(messageService))
		// 部署队列调度器
		schedulers.push(new DeploymentQueueScheduler())
	}

	// 启动所有配置的调度器
	for (const scheduler of schedulers) {
		await scheduler.start()
	}
	logger.info(`已启动以下调度器: ${schedulers.map(s => s.constructor.name).join(', ')}`)

	let relatedKeywordsResultListener: RelatedKeywordsResultListener | null = null
	let keywordsTrendResultListener: KeywordsTrendResultListener | null = null
	let translationTaskListener: TranslationTaskListener | null = null


	// 非fafafa环境或开发环境启动
	if (!isFaFafa || isDev) {
		// 翻译任务监听器
		translationTaskListener = new TranslationTaskListener(mqClient)
		await translationTaskListener.startConsuming(5)
		logger.info("translationTaskListener 已启动")

		// 相关关键词结果监听器
		relatedKeywordsResultListener = new RelatedKeywordsResultListener(mqClient)
		await relatedKeywordsResultListener.startConsuming(10)
		logger.info("relatedKeywordsResultListener 已启动")

		// 关键词趋势结果监听器
		keywordsTrendResultListener = new KeywordsTrendResultListener(mqClient)
		await keywordsTrendResultListener.startConsuming(10)
		logger.info("keywordsTrendResultListener 已启动")
	}

	// 创建并启动 HTTP 服务
	const httpServer = new HttpServer(httpPort, {
		schedulers,
		messageService,
	})

	// 启动 HTTP 服务
	httpServer.start()
	logger.info(`HTTP server started on port ${httpPort}`)

	// 处理进程退出
	process.on("SIGTERM", async () => {
		for (const scheduler of schedulers) {
			await scheduler.stop()
		}

		if (!isFaFafa || isDev) {
			if (translationTaskListener) {
				await translationTaskListener.destroy()
			}
			if (relatedKeywordsResultListener) {
				await relatedKeywordsResultListener.destroy()
			}
			if (keywordsTrendResultListener) {
				await keywordsTrendResultListener.destroy()
			}
		}

		await messageService.destroy()
		logger.info("Scheduler stopped")

		process.exit(0)
	})
}

main().catch(console.error)

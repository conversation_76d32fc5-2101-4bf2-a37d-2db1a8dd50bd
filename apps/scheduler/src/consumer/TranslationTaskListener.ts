import { getLogger } from "@repo/logger"
import { TaskQueueListener } from "@repo/utils/amqp"
import { prisma } from "@repo/db"
import { RpaMessage, BACKGROUND_TASK_STATUS } from "@repo/shared-types"
import { AMQPClient } from "@repo/utils/amqp"
import { buildTranslationPrompt } from "@repo/utils/server"
import { translateText } from "@repo/utils/server"
import { safeRepairParseJson } from "@repo/utils"

const logger = getLogger("TranslationTaskListener")

interface TranslationTaskMessage {
	taskId: string
	type: string
	projectId: string
	userId: string
	parameters: {
		contentType: string
		contentId: string
		sourceLocale: string
		targetLocales: string[]
		fieldsToTranslate?: string[]
		extraParams?: Record<string, any>
	}
}

export class TranslationTaskListener extends TaskQueueListener {
	constructor(mqClient: AMQPClient) {
		const queueName = `${process.env.TASK_QUEUE_NAME}_translation_tasks`
		super(mqClient, queueName, "")
	}

	async execute(message: RpaMessage): Promise<any> {
		const taskData = message.data as unknown as TranslationTaskMessage
		const { taskId, parameters, type } = taskData
		const { contentId, sourceLocale, targetLocales } = parameters

		logger.info(`[TranslationTaskListener] 开始处理翻译任务`, {
			taskId,
			type,
			contentId,
		})

		try {
			// 更新任务状态为处理中
			await this.updateTaskStatus(taskId, BACKGROUND_TASK_STATUS.PROCESSING)

			// 获取源内容
			const sourceContent = await this.getSourceContent(type, contentId)
			if (!sourceContent) {
				throw new Error(`未找到源内容: ${type}/${contentId}/${sourceLocale}`)
			}

			// 执行翻译
			const translationResults = await this.translateContent(
				sourceContent,
				sourceLocale,
				targetLocales,
				type, // 使用 taskData.type 作为 contentType
				taskData.projectId,
			)

			// 保存翻译结果
			await this.saveTranslationResults(
				type, // 使用 type 而不是 contentType
				contentId,
				sourceLocale,
				translationResults,
			)

			// 更新任务状态为成功
			await this.updateTaskStatus(taskId, BACKGROUND_TASK_STATUS.SUCCESS, {
				translatedLocales: Object.keys(translationResults),
				translationCount: Object.keys(translationResults).length,
			})

			logger.info(`[TranslationTaskListener] 翻译任务完成`, {
				taskId,
				translatedLocales: Object.keys(translationResults),
			})
		} catch (error) {
			logger.error(`[TranslationTaskListener] 翻译任务失败`, {
				taskId,
				error: error,
			})

			// 获取当前任务信息
			const task = await prisma.backgroundTask.findUnique({
				where: { id: taskId },
			})

			if (!task) {
				logger.error(`[TranslationTaskListener] 任务不存在: ${taskId}`)
				return
			}

			// 增加重试次数
			const newRetryCount = task.retryCount + 1

			if (newRetryCount < task.maxRetries) {
				// 还可以重试，更新重试次数并重新入队
				await prisma.backgroundTask.update({
					where: { id: taskId },
					data: {
						retryCount: newRetryCount,
						errorMessage: error as unknown as string,
						updatedAt: new Date(),
					},
				})

				// 延迟后重新发送到队列
				setTimeout(async () => {
					try {
						await this.requeueTask(taskData)
						logger.info(`[TranslationTaskListener] 任务重新入队`, {
							taskId,
							retryCount: newRetryCount,
						})
					} catch (requeueError) {
						logger.error(`[TranslationTaskListener] 重新入队失败`, {
							taskId,
							error: requeueError,
						})
					}
				}, 5000) // 5秒后重试
			} else {
				// 超过最大重试次数，标记为失败
				await this.updateTaskStatus(
					taskId,
					BACKGROUND_TASK_STATUS.FAILED,
					undefined,
					error as unknown as string,
				)
			}
		}
	}

	private async getSourceContent(
		type: string,
		contentId: string,
	): Promise<any> {
		// 解析类型约定：ProjectSiteSetting_${ProjectLocaleSiteSettingType} 或 ProjectGame_${ProjectGameLocaleType}
		if (type.startsWith("ProjectSiteSetting_")) {
			const setting = await prisma.projectLocaleSiteSetting.findUnique({
				where: { id: contentId },
			})
			return setting?.content
		} else if (type.startsWith("ProjectGame_")) {
			const gameLocale = await prisma.projectGameLocale.findUnique({
				where: { id: contentId },
			})
			return gameLocale?.content
		} else {
			throw new Error(`不支持的内容类型: ${type}`)
		}
	}

	private async translateContent(
		sourceContent: any,
		sourceLocale: string,
		targetLocales: string[],
		type?: string,
		projectId?: string,
	): Promise<Record<string, any>> {
		const results: Record<string, any> = {}
		const maxRetries = 3

		// 并发翻译所有目标语言
		const translationPromises = targetLocales.map(async (targetLocale) => {
			let retryCount = 0

			while (retryCount < maxRetries) {
				try {
					const translatedContent = await this.translateWithPrompt(
						sourceContent,
						sourceLocale,
						targetLocale,
						type,
						projectId,
					)

					return { locale: targetLocale, content: translatedContent }
				} catch (error) {
					retryCount++
					logger.error(
						`[TranslationTaskListener] 翻译到 ${targetLocale} 失败 (第${retryCount}次重试)`,
						{ error: error },
					)

					if (retryCount >= maxRetries) {
						logger.error(
							`[TranslationTaskListener] 翻译到 ${targetLocale} 最终失败，已达到最大重试次数`,
							{ error: error },
						)
						throw error
					}

					// 重试前等待一段时间
					await new Promise((resolve) => setTimeout(resolve, 1000 * retryCount))
				}
			}
		})

		// 等待所有翻译完成
		const translationResults = await Promise.allSettled(translationPromises)

		// 处理翻译结果
		translationResults.forEach((result, index) => {
			const targetLocale = targetLocales[index]

			if (result.status === "fulfilled" && result.value) {
				results[result.value.locale] = result.value.content
			} else {
				logger.error(
					`[TranslationTaskListener] 翻译到 ${targetLocale} 最终失败`,
					{ error: result.status === "rejected" ? result.reason : "未知错误" },
				)
			}
		})

		return results
	}

	private async translateWithPrompt(
		sourceContent: any,
		sourceLocale: string,
		targetLocale: string,
		type?: string,
		projectId?: string,
	): Promise<any> {
		// 将整个JSON内容传给translateText方法
		const contentStr =
			typeof sourceContent === "string"
				? sourceContent
				: JSON.stringify(sourceContent, null, 2)

		const prompt = await buildTranslationPrompt(
			type || "default",
			sourceLocale,
			targetLocale,
			contentStr,
			projectId,
		)

		const translationResult = await translateText(contentStr, {
			sourceLanguage: sourceLocale,
			targetLanguage: targetLocale,
			customPrompt: prompt,
		})

		// 尝试解析为JSON，如果失败则返回字符串
		return safeRepairParseJson(translationResult)
	}

	private async saveTranslationResults(
		contentType: string,
		sourceContentId: string,
		sourceLocale: string,
		translationResults: Record<string, any>,
	): Promise<void> {
		if (contentType.startsWith("ProjectGame_")) {
			await this.saveGameContentTranslations(
				contentType,
				sourceContentId,
				sourceLocale,
				translationResults,
			)
		} else if (contentType.startsWith("ProjectSiteSetting_")) {
			await this.saveProjectSiteSettingTranslations(
				contentType,
				sourceContentId,
				sourceLocale,
				translationResults,
			)
		} else {
			throw new Error(`不支持的内容类型: ${contentType}`)
		}
	}

	private async saveProjectSiteSettingTranslations(
		_contentType: string, // 保留参数但标记为未使用
		sourceContentId: string,
		_sourceLocale: string, // 保留参数但标记为未使用
		translationResults: Record<string, any>,
	): Promise<void> {
		// 获取源内容的项目ID
		const sourceContent = await prisma.projectLocaleSiteSetting.findUnique({
			where: { id: sourceContentId },
			select: { projectId: true, type: true },
		})

		if (!sourceContent) {
			throw new Error(`源内容不存在: ${sourceContentId}`)
		}

		const { projectId, type } = sourceContent

		// 为每个目标语言保存翻译结果
		for (const [locale, content] of Object.entries(translationResults)) {
			await prisma.projectLocaleSiteSetting.upsert({
				where: {
					project_locale_site_setting_unique: {
						projectId,
						locale,
						type,
					},
				},
				update: {
					content,
					status: "COMPLETED",
					updatedAt: new Date(),
				},
				create: {
					projectId,
					locale,
					type,
					content,
					status: "COMPLETED",
				},
			})
		}
	}

	private async saveGameContentTranslations(
		_contentType: string, // 保留参数但标记为未使用
		sourceContentId: string,
		_sourceLocale: string, // 保留参数但标记为未使用
		translationResults: Record<string, any>,
	): Promise<void> {
		// 获取源游戏内容
		const sourceContent = await prisma.projectGameLocale.findUnique({
			where: { id: sourceContentId },
			select: {
				projectId: true,
				gameId: true,
				type: true,
				contentId: true,
				sort: true,
			},
		})

		if (!sourceContent) {
			throw new Error(`源游戏内容不存在: ${sourceContentId}`)
		}

		const { projectId, gameId, type, contentId, sort } = sourceContent

		// 为每个目标语言保存翻译结果
		for (const [locale, content] of Object.entries(translationResults)) {
			await prisma.projectGameLocale.upsert({
				where: {
					project_game_locale_content_id_locale_unique: {
						contentId: contentId || `${gameId}_${type}_${locale}`,
						locale,
					},
				},
				update: {
					content,
					status: "COMPLETED",
					updatedAt: new Date(),
				},
				create: {
					projectId,
					gameId,
					type,
					locale,
					contentId: contentId || `${gameId}_${type}_${locale}`,
					content,
					sort: sort || 0,
					status: "COMPLETED",
				},
			})
		}
	}

	private async updateTaskStatus(
		taskId: string,
		status: string,
		result?: Record<string, any>,
		errorMessage?: string,
	): Promise<void> {
		const updateData: any = {
			status,
			updatedAt: new Date(),
		}

		if (status === BACKGROUND_TASK_STATUS.PROCESSING) {
			updateData.startedAt = new Date()
		}

		if (
			status === BACKGROUND_TASK_STATUS.SUCCESS ||
			status === BACKGROUND_TASK_STATUS.FAILED
		) {
			updateData.completedAt = new Date()
		}

		if (result) {
			updateData.result = result
		}

		if (errorMessage) {
			updateData.errorMessage = errorMessage
		}

		await prisma.backgroundTask.update({
			where: { id: taskId },
			data: updateData,
		})
	}

	private async requeueTask(taskData: TranslationTaskMessage): Promise<void> {
		// 重新发送消息到队列
		// 这里需要使用MessageService来发送消息
		// 由于我们在TaskQueueListener中，可以通过父类的方法来发送
		// 但这需要修改TaskQueueListener的实现，暂时先记录日志
		logger.info(`[TranslationTaskListener] 需要重新入队任务`, {
			taskId: taskData.taskId,
		})
	}
}

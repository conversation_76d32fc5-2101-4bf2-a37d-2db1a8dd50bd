import { StarDiamondProductCode, isStarDiamondProduct } from "@lib/consts"

import { prisma } from "@repo/db"
import { getLogger } from "@repo/logger"
import { type GoodsInfo, GoodsType, PaymentError } from "@repo/payment"
import { BaseGoodsService, type TradeRecord } from "@repo/payment/trade"
import {
	BenefitCode,
	UsageAction,
	isMembership,
	isMembershipUpgrade,
} from "@repo/shared-types"
import { createUserBenefit, getUserMembership } from "./BenefitService"
import { getActiveProducts, getProductByCode } from "./ProductService"

const logger = getLogger("GoodsService")

const starDiamoundUnit = "星钻"

// 定义星钻充值折扣计算函数
const calculateStarDiamondDiscount = (quantity: number): number => {
	if (quantity <= 500) return 1.0 // 无折扣
	if (quantity <= 1200) return 0.9 // 9折
	if (quantity <= 3000) return 0.8 // 8折
	return 0.7 // 7折
}

// 计算星钻充值价格
const calculateStarDiamondPrice = (quantity: number): number => {
	// 基础价格：1元购买10个星钻
	const basePrice = Math.floor((quantity / 10) * 100) // 单位：分
	const discount = calculateStarDiamondDiscount(quantity)
	return Math.floor(basePrice * discount)
}

export class GoodsService extends BaseGoodsService {
	public async getGoodsById(goodsId: string): Promise<GoodsInfo | null> {
		const product = await getProductByCode(goodsId)
		if (!product) {
			return null
		}

		// 对星钻产品特殊处理
		if (isStarDiamondProduct(goodsId)) {
			const goodsInfo: GoodsInfo = {
				id: product.code,
				type: GoodsType.Recharge,
				quantity: product.credits,
				unit: starDiamoundUnit,
				name: product.name,
				price: product.discountPrice,
				currency: product.currency,
			}
			return goodsInfo
		}

		// 其他普通产品
		const goodsInfo: GoodsInfo = {
			id: product.code,
			type: GoodsType.Subscription,
			name: product.name,
			price: product.discountPrice,
			currency: product.currency,
		}
		return goodsInfo
	}

	public getGoodsList(type?: GoodsType): Promise<GoodsInfo[]> {
		return getActiveProducts().then((products) => {
			// 根据请求的商品类型筛选
			let filteredProducts = products
			if (type === GoodsType.Recharge) {
				// 筛选星钻产品
				filteredProducts = products.filter((p) => p.code.startsWith("xz-"))
			} else if (type === GoodsType.Subscription) {
				// 筛选非星钻产品
				filteredProducts = products.filter((p) => !p.code.startsWith("xz-"))
			}

			return filteredProducts.map((product) => {
				// 对星钻产品特殊处理
				if (
					product.code.startsWith("xz-") &&
					product.code !== StarDiamondProductCode.CUSTOM
				) {
					const goodsInfo: GoodsInfo = {
						id: product.code,
						type: GoodsType.Recharge,
						quantity: product.credits,
						unit: starDiamoundUnit,
						name: product.name,
						price: product.discountPrice,
						currency: product.currency,
					}
					return goodsInfo
				}

				// 其他普通产品
				const goodsInfo: GoodsInfo = {
					id: product.code,
					type: product.code.startsWith("xz-")
						? GoodsType.Recharge
						: GoodsType.Subscription,
					name: product.name,
					price: product.discountPrice,
					currency: product.currency,
				}
				return goodsInfo
			})
		})
	}

	// 处理星钻充值交易创建前的逻辑
	protected async handleRechargeBeforeCreate(
		userId: string,
		goods: GoodsInfo,
		tradeNo: string,
	): Promise<Record<string, unknown>> {
		const goodId = goods.id
		// 如果是自定义星钻数量
		if (StarDiamondProductCode.CUSTOM === goodId) {
			try {
				const quantity = goods.quantity ?? 0
				if (!Number.isNaN(quantity) && quantity > 0) {
					// 返回计算后的元数据
					return {
						quantity,
						unit: starDiamoundUnit,
						price: calculateStarDiamondPrice(quantity), // 折后价（分）
					}
				}
			} catch (err) {
				logger.error("处理自定义星钻数量失败:", err)
				throw new PaymentError("无效的星钻数量")
			}
		}

		const product = await getProductByCode(goodId)
		if (!product) {
			throw new PaymentError(`未找到星钻产品: ${goodId}`)
		}

		return {
			quantity: product.credits,
			unit: starDiamoundUnit,
			basePrice: product.price,
			discount: product.discount,
			discountPrice: product.discountPrice,
		}
	}

	protected handleOneTimePurchaseSuccess(
		userId: string,
		goods: GoodsInfo,
		trade: TradeRecord,
	): Promise<void> {
		return Promise.reject(new PaymentError("Not implemented"))
	}

	protected async handleSubscriptionBeforeCreate(
		userId: string,
		goods: GoodsInfo,
		tradeNo: string,
	): Promise<Record<string, unknown>> {
		if (isMembership(goods.id as BenefitCode)) {
			const membership = await getUserMembership(userId)
			if (
				membership &&
				isMembershipUpgrade(membership as BenefitCode, goods.id as BenefitCode)
			) {
				throw new PaymentError("已订阅高级套餐，不能订阅低级套餐，仅能升级套餐")
			}
		}
		return {}
	}

	protected async handleSubscriptionPurchaseSuccess(
		userId: string,
		goods: GoodsInfo,
		trade: TradeRecord,
	): Promise<void> {
		// 创建订阅
		await createUserBenefit(userId, goods.id, trade.id)

		logger.info(
			`用户${userId}购买订阅：${goods.name || goods.id}，交易ID：${trade.id} 成功。`,
		)
	}

	protected async handleRechargeSuccess(
		userId: string,
		goods: GoodsInfo,
		trade: TradeRecord,
	): Promise<void> {
		const productCode = goods.id
		const quantity = goods.quantity!

		try {
			// 使用事务保证数据一致性
			await prisma.$transaction(async (tx) => {
				// 1. 增加用户星钻余额
				await tx.user.update({
					where: { id: userId },
					data: {
						credits: {
							increment: quantity,
						},
					},
				})

				// 2. 先查询或创建星钻权益记录
				let userBenefit = await tx.userBenefit.findFirst({
					where: {
						userId,
						benefitCode: "credits",
					},
				})

				if (!userBenefit) {
					// 创建一个星钻权益记录
					userBenefit = await tx.userBenefit.create({
						data: {
							userId,
							productCode,
							benefitCode: BenefitCode.Credits,
							tradeId: trade.id,
							startTime: new Date(),
							endTime: new Date("2099-12-31"), // 永久有效
							availableQuantity: quantity,
							usedQuantity: 0,
							metadata: {
								name: "星钻充值",
							},
						},
					})
				}

				// 3. 创建星钻充值记录
				await tx.userBenefitUsage.create({
					data: {
						userId,
						userBenefitId: userBenefit.id,
						benefitCode: BenefitCode.Credits,
						quantity,
						action: UsageAction.RECHARGE,
						description: `充值${quantity}个星钻`,
					},
				})

				logger.info(
					`用户${userId}充值星钻成功：${quantity}个，交易ID：${trade.id}`,
				)
			})
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : String(error)
			logger.error(`用户${userId}充值星钻失败:`, error)
			throw new PaymentError(`星钻充值失败: ${errorMessage}`)
		}
	}
}

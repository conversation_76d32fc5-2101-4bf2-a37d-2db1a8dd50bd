import { BusinessError } from "@lib/errors"
import { prisma } from "@repo/db"
import logger from "@repo/logger"
import { createBackgroundTask } from "./BackgroundTaskService"

/**
 * 翻译任务相关的接口定义
 */
export interface TranslationTaskParams {
	// 翻译数据 ID  => ProjectLocaleSiteSetting 、ProjectGameLocale 、ProjectArticleLocale
	contentId: string
	// 翻译的内容类型 (metadata, content, etc.)
	contentType: string
	// 源语言
	sourceLocale: string
	// 目标语言列表
	targetLocales: string[]
	// 需要翻译的字段
	fieldsToTranslate?: string[]
	// 额外参数
	[key: string]: any
}

export interface TranslationTaskInfo {
	// 翻译数据 ID  => ProjectLocaleSiteSetting 、ProjectGameLocale 、ProjectArticleLocale
	contentId: string
	// 翻译数据类型
	contentType: string
}

export interface TranslationLanguage {
	sourceLocale: string
	targetLocales: string[]
}

/**
 * 创建翻译任务
 *
 * @param type 翻译任务类型
 * @param projectId 项目ID
 * @param userId 用户ID
 * @param params 翻译任务参数
 * @param queueName 可选的队列名称
 * @returns 翻译任务ID
 */
async function createTranslationTask(
	type: string,
	projectId: string,
	userId: string,
	params: TranslationTaskParams,
): Promise<string> {
	// scheduler 监听翻译任务时，会根据type区分不同的翻译任务，查询不同的数据表。需要确保type的值不冲突
	// 约定格式：ProjectSiteSetting_${ProjectLocaleSiteSettingType} 和 ProjectGame_${ProjectGameLocaleType}
	const queueName = `${process.env.TASK_QUEUE_NAME}_translation_tasks`
	logger.info(`创建翻译任务=> ${queueName}`, { projectId, userId, params })
	const { contentId, sourceLocale, targetLocales } = params

	const title = `翻译任务: ${type} (${contentId}) `
	const description = `将 ${sourceLocale} 翻译为 ${targetLocales.join(", ")}`
	return await createBackgroundTask({
		queueName,
		type,
		projectId,
		userId,
		title,
		description,
		parameters: params,
	})
}

/**
 * 创建单个翻译任务
 *
 * @param type 内容类型
 * @param projectId 项目ID
 * @param userId 用户ID
 * @returns 翻译任务ID，如果不需要创建则返回null
 */
export async function createSingleTranslationTask(
	type: string,
	projectId: string,
	userId: string,
	info: TranslationTaskInfo,
	language?: TranslationLanguage,
): Promise<string | null> {
	try {
		const { contentType, contentId } = info

		let sourceLocale = language?.sourceLocale
		let targetLocales = language?.targetLocales

		if (!sourceLocale || !targetLocales) {
			const setting = await prisma.projectSiteSetting.findUnique({
				where: { projectId },
				select: {
					defaultLocale: true,
					languanges: true,
				},
			})
			if (!setting) {
				throw new BusinessError(`projectSiteSetting not found`)
			}
			sourceLocale = setting.defaultLocale
			targetLocales = (setting.languanges as any[]).filter(
				(v) => v !== sourceLocale,
			)
		}

		// 构建翻译任务参数
		const translationParams: TranslationTaskParams = {
			contentType,
			contentId,
			sourceLocale,
			targetLocales,
		}

		const translationTaskId = await createTranslationTask(
			type,
			projectId,
			userId,
			translationParams,
		)

		return translationTaskId
	} catch (error) {
		console.error("创建翻译任务失败:", error)
		return null
	}
}

/**
 * 批量创建翻译任务
 *
 * @param translationTasks 翻译任务信息列表
 * @param type 内容类型
 * @param projectId 项目ID
 * @param userId 用户ID
 * @param locale 源语言
 * @param gameParams 游戏相关参数（可选）
 * @param options 创建选项
 * @returns 创建的翻译任务ID列表
 */
export async function createBatchTranslationTasks(
	type: string,
	projectId: string,
	userId: string,
	translationTasks: TranslationTaskInfo[],
): Promise<string[]> {
	const bgTaskIds = []

	if (translationTasks.length === 0) {
		return []
	}
	const setting = await prisma.projectSiteSetting.findUnique({
		where: { projectId },
		select: {
			defaultLocale: true,
			languanges: true,
		},
	})
	if (!setting) {
		throw new BusinessError(`projectSiteSetting not found`)
	}

	const language: TranslationLanguage = {
		sourceLocale: setting?.defaultLocale,
		targetLocales: (setting.languanges as any[]).filter(
			(v) => v !== setting?.defaultLocale,
		),
	}

	for (const task of translationTasks) {
		try {
			// 使用统一的单个翻译任务创建接口
			const translationTaskId = await createSingleTranslationTask(
				type,
				projectId,
				userId,
				task,
				language,
			)

			if (translationTaskId) {
				bgTaskIds.push(translationTaskId)
			}
		} catch (error) {
			console.error("创建翻译任务失败:", error)
		}
	}

	return bgTaskIds
}

import type { Benefit, User, UserBenefit } from "@repo/db"
import { type Prisma, prisma } from "@repo/db"
import { logger } from "@repo/logger"
import {
	BenefitCode,
	BenefitCostRules,
	BusinessError,
	ProductStatus,
	UsageAction,
	isMembership,
	UserRole,
} from "@repo/shared-types"
import dayjs from "dayjs"
import { authUser } from "@repo/auth/server"

export async function saveOrUpdateBenefit(
	benefit: Omit<Benefit, "id" | "createdAt" | "updatedAt">,
) {
	return prisma.benefit.upsert({
		where: { code: benefit.code },
		update: benefit,
		create: benefit,
	})
}

/**
 * 创建用户权益
 * 在用户购买成功时调用，插入对应的权益数据
 * @param userId 用户ID
 * @param productCode 产品代码
 * @param tradeId 交易ID（可选）
 * @returns 创建的权益记录数组
 * @throws 如果产品不存在或创建失败则抛出异常
 */
export async function createUserBenefit(
	userId: string,
	productCode: string,
	tradeId?: string,
) {
	// 使用事务确保数据一致性
	return prisma.$transaction(async (tx) => {
		// 查询产品信息
		const product = await tx.product.findUnique({
			where: { code: productCode },
			include: {
				benefitRules: {
					include: {
						benefit: true,
					},
				},
			},
		})

		if (!product) {
			throw new BusinessError(`产品不存在: ${productCode}`)
		}

		// 检查产品状态
		if (product.status !== ProductStatus.ACTIVE) {
			throw new BusinessError(`产品状态不可用: ${product.status}`)
		}

		// 计算权益的开始时间
		const startTime = new Date()
		const userBenefits = []

		// 为每个产品关联的权益创建用户权益记录
		for (const rule of product.benefitRules) {
			// 计算结束时间
			let endTime = dayjs("2099-12-31T23:59:59.999Z").toDate()
			if (rule.validDays > 0) {
				endTime = dayjs(startTime).add(rule.validDays, "day").toDate()
			}

			const isMembershipBenefit = isMembership(rule.benefitCode as BenefitCode)

			// 创建用户权益记录
			const userBenefit = await tx.userBenefit.create({
				data: {
					userId,
					productCode,
					tradeId,
					benefitCode: rule.benefitCode,
					startTime,
					endTime,
					// 如果为会员订阅，记录用户的信用点数
					availableQuantity: isMembershipBenefit
						? product.credits
						: rule.maxQuantity,
					usedQuantity: 0,
					metadata: {
						productName: product.name,
						benefitName: rule.benefit.name,
					},
				},
			})

			// 如果是会员订阅，增加用户的信用点数
			if (isMembershipBenefit) {
				await tx.user.update({
					where: { id: userId },
					data: {
						credits: {
							increment: product.credits,
						},
					},
				})
			}

			userBenefits.push(userBenefit)

			// 记录订阅行为
			await tx.userBenefitUsage.create({
				data: {
					userId,
					userBenefitId: userBenefit.id,
					benefitCode: rule.benefitCode,
					quantity: rule.maxQuantity,
					action: UsageAction.SUBSCRIPTION,
					description: `订阅${product.name}套餐，获得${rule.benefit.name}权益`,
				},
			})

			// 单独记录新增的信用点数
			if (isMembershipBenefit) {
				await tx.userBenefitUsage.create({
					data: {
						userId,
						userBenefitId: userBenefit.id,
						benefitCode: rule.benefitCode,
						quantity: product.credits,
						action: UsageAction.INCR,
						description: `订阅${product.name}套餐，获得${product.credits}星钻`,
					},
				})
			}
		}

		return userBenefits
	})
}

/**
 * 检查用户是否有有效的权益
 * @param userId 用户ID
 * @param benefitCode 权益代码
 * @param quantity 可选参数，需要检查的权益份数
 * @returns 如果有有效权益返回true，否则返回false，以及错误信息，用户权益，权益信息，用户信息
 */
export async function hasValidBenefit({
	userId,
	benefitCode,
	quantity = 1,
}: {
	userId: string
	benefitCode: string
	quantity?: number
}): Promise<
	[boolean, string, UserBenefit | null, Benefit | null, User | null]
> {
	const benefit = await prisma.benefit.findUnique({
		where: {
			code: benefitCode,
		},
	})
	if (!benefit) {
		logger.info(`权益${benefitCode}不存在`)
		return [
			false,
			`非常抱歉，此${benefitCode}权益不存在，请重新购买套餐`,
			null,
			null,
			null,
		]
	}

	const dbUser = await prisma.user.findUnique({
		where: {
			id: userId,
		},
	})
	if (!dbUser) {
		logger.info(`用户${userId}不存在`)
		return [
			false,
			`非常抱歉，您当前没有【${benefit.name}】权益，请购买更高级别的套餐`,
			null,
			benefit,
			null,
		]
	}

	const now = new Date()
	const userBenefit = await prisma.userBenefit.findFirst({
		where: {
			userId,
			benefitCode,
		},
		orderBy: [{ endTime: "desc" }, { createdAt: "desc" }],
	})

	// 没有任何花费的权益，管理员不检查
	if (
		benefit.costRules === BenefitCostRules.NO_COST &&
		dbUser.role === UserRole.Admin
	) {
		return [
			true,
			`您的【${benefitCode}】权益有效`,
			userBenefit,
			benefit,
			dbUser,
		]
	}

	// 检查是否存在
	if (!userBenefit) {
		logger.info(`用户${userId}没有此权益${benefitCode}`)
		return [
			false,
			`非常抱歉，您当前没有【${benefit.name}】权益，请购买更高级别的套餐`,
			null,
			benefit,
			dbUser,
		]
	}

	// 检查是否在有效期内
	if (userBenefit.endTime && userBenefit.endTime < now) {
		logger.info(`用户${userId}的此权益${benefitCode}已过期`)
		return [
			false,
			`非常抱歉，您的【${benefit.name}】权益已过期，请重新购买套餐后使用`,
			userBenefit,
			benefit,
			dbUser,
		]
	}

	const costRules = benefit.costRules as any
	if (costRules === BenefitCostRules.PER_UNIT) {
		if (dbUser.credits < quantity * (benefit.cost || 0)) {
			logger.info(
				`用户${userId}的此权益${benefit.name}所需要的星钻数量不足，count:${quantity}*cost:${benefit.cost} 大于可用数量:${dbUser.credits}`,
			)
			return [
				false,
				`非常抱歉，当前【${benefit.name}】所需星钻数量不足，共需要${quantity * (benefit.cost || 0)}个星钻，
				您当前可用星钻数量为${dbUser.credits}个，请购买更高套餐或者单独购买星钻数量后使用`,
				userBenefit,
				benefit,
				dbUser,
			]
		}
	} else if (costRules === BenefitCostRules.PER_COUNT) {
		if (userBenefit.availableQuantity < quantity * (benefit.cost || 1)) {
			logger.info(
				`用户${userId}的此权益${benefit.name}可用次数不足，需要次数:${quantity}*cost:${benefit.cost} 大于可用次数:${userBenefit.availableQuantity}`,
			)
			return [
				false,
				`非常抱歉，当前【${benefit.name}】可用次数不足，共需要${quantity * (benefit.cost || 1)}次，
				您当前可用次数为${userBenefit.availableQuantity}次，请购买更高套餐后使用`,
				userBenefit,
				benefit,
				dbUser,
			]
		}
	}

	return [true, `您的【${benefit.name}】权益有效`, userBenefit, benefit, dbUser]
}

/**
 * 获取用户会员级别
 * @param userId 用户ID
 * @returns 用户的会员级别，如果没有会员权益则返回null
 */
export async function getUserMembership(
	userId: string,
): Promise<string | null> {
	const now = new Date()

	const memberBenefit = await prisma.userBenefit.findFirst({
		where: {
			userId,
			benefitCode: {
				in: [
					BenefitCode.V0,
					BenefitCode.V1,
					BenefitCode.V2,
					BenefitCode.V3,
					BenefitCode.V4,
					BenefitCode.Ep,
				],
			},
			endTime: { gt: now },
		},
		orderBy: {
			// 按创建时间倒序，获取最新的会员权益
			createdAt: "desc",
		},
	})

	return memberBenefit ? memberBenefit.benefitCode : null
}

/**
 * 减扣用户权益，外部调用时使用事务确保数据一致性
 * @param userId 用户ID
 * @param benefitCode 权益代码
 * @param quantity 权益份数
 * @returns 处理结果
 */
export async function decrUserBenefit(
	userId: string,
	benefitCode: string,
	quantity: number,
	tx: Prisma.TransactionClient,
	description?: string,
): Promise<void> {
	// 首先获取权益规则信息
	const res = await hasValidBenefit({
		userId,
		benefitCode,
		quantity,
	})

	const [isValid, errorMessage, userBenefit, benefit, dbUser] = res
	if (!isValid) {
		throw new BusinessError(errorMessage)
	}

	const costRule = benefit?.costRules as any
	const isFreeType = costRule === BenefitCostRules.NO_COST
	// 如果不需要扣费，直接返回
	if (isFreeType) {
		logger.info(
			`用户${userId}的此权益${benefitCode}:${benefit?.name}不需要扣费`,
		)
		return
	}
	let costCredit = benefit?.cost || 0
	let desc = description
	// 按单位份数扣费
	if (costRule === BenefitCostRules.PER_UNIT) {
		costCredit *= quantity
		// 扣减用户星钻，增加用户权益使用记录
		await tx.user.update({
			where: { id: userId },
			data: {
				credits: dbUser!.credits - costCredit,
			},
		})
		desc = `使用${benefit?.name}权益,扣减${costCredit}星钻`
	} else if (costRule === BenefitCostRules.PER_COUNT) {
		costCredit = (benefit?.cost ?? 1) * quantity
		await tx.userBenefit.update({
			data: {
				availableQuantity: userBenefit!.availableQuantity,
			},
			where: { id: userBenefit!.id },
		})
		desc = `使用${benefit?.name}权益,扣减${costCredit}次`
	}
	await tx.userBenefitUsage.create({
		data: {
			userId,
			userBenefitId: userBenefit!.id,
			benefitCode,
			quantity: costCredit,
			action: UsageAction.DECR,
			description: desc,
		},
	})

	logger.info(
		`用户${userId}的此权益${benefitCode}:${benefit?.name}扣费成功，扣减${costCredit}星钻`,
	)
}

/**
 * 回滚权益使用（增加星钻，创建使用记录）,外部调用时使用事务确保数据一致性
 * @param userId 用户ID
 * @param benefitCode 权益代码
 * @param quantity 使用数量
 * @param description 描述信息
 */
export async function rollbackBenefitUsage(
	userId: string,
	benefitCode: string,
	quantity: number,
	tx: Prisma.TransactionClient,
	description?: string,
): Promise<void> {
	const benefit = await tx.benefit.findUnique({
		where: { code: benefitCode },
	})

	if (!benefit) {
		logger.error(`权益不存在: ${benefitCode}，不进行回滚`)
		return
	}

	const costRule = benefit.costRules as any
	const isFreeType = costRule === BenefitCostRules.NO_COST

	if (!isFreeType) {
		let costCredit = benefit.cost || 0
		if (costRule === BenefitCostRules.PER_UNIT) {
			costCredit *= quantity
		}

		await tx.user.update({
			where: { id: userId },
			data: {
				credits: { increment: costCredit },
			},
		})
	}
	const userBenefit = await tx.userBenefit.findFirst({
		where: {
			userId,
			benefitCode,
		},
	})
	if (!userBenefit) {
		logger.error(`用户${userId}没有此权益${benefitCode}，不进行回滚`)
		return
	}
	await tx.userBenefitUsage.create({
		data: {
			userId,
			benefitCode,
			userBenefitId: userBenefit.id,
			quantity,
			action: UsageAction.INCR,
			description: description || `回滚${benefit.name}扣减的星钻数量`,
		},
	})

	logger.info(
		`用户${userId}的权益${benefitCode}:${benefit.name}回滚成功，数量: ${quantity}`,
	)
}

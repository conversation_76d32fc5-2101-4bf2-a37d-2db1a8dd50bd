"use client"

import Sidebar from "./Sidebar"
import TopNavbar from "./TopNavbar"
import { cn } from "@repo/ui/utils"

import { SidebarProvider } from "@repo/ui/components"

export default function DashboardShell({
	children,
}: { children: React.ReactNode }) {
	return (
		<div className="flex h-screen bg-background">
			<SidebarProvider defaultOpen={true}>
				{/* 侧边栏 */}
				<Sidebar />

				{/* 主内容区域 */}
				<div className="flex-1 flex-grow flex-col overflow-hidden">
					{/* 顶部导航栏 */}
					<TopNavbar />

					{/* 主内容 */}
					<main
						className={cn(
							"h-[calc(100vh-4rem)] max-w-full bg-gray-100 overflow-y-auto box-content",
							"scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 scrollbar-rounded-full hover:scrollbar-thu hover:scrollbar-thumb-gray-400",
						)}
					>
						{children}
					</main>
				</div>
			</SidebarProvider>
		</div>
	)
}

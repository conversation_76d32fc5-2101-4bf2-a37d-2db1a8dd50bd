"use client"

import { <PERSON> } from "@repo/i18n"
import {
	BarChart,
	Building2,
	Cloud,
	Gift,
	LayoutDashboard,
	LineChart,
	Monitor,
	PlayCircle,
	Bot,
	Search,
	Settings,
	Book,
	Timer,
	Users,
	DollarSign,
	Tag,
	Package,
	ShieldCheck,
	Star,
	BookText,
	BookType,
} from "lucide-react"
import { useTranslations } from "next-intl"
import { usePathname } from "next/navigation"
import { BenefitCode } from "@repo/shared-types"
import {
	Sidebar as ShadcnSidebar,
	SidebarContent,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuItem,
	SidebarMenuButton,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
	Collapsible,
	CollapsibleTrigger,
	CollapsibleContent,
} from "@repo/ui/components"
import { ChevronDown } from "lucide-react"
import { UserRole } from "@repo/shared-types"
import { useAuthUser } from "@repo/auth/react"

export interface MenuItem {
	name: string
	key: string
	href?: string
	icon: React.ReactNode
	roles?: string[]
	benefits?: BenefitCode[]
	children?: MenuItem[]
}

interface MenuGroup {
	title: string
	roles?: string[]
	items: MenuItem[]
}

export default function Sidebar() {
	const t = useTranslations("Dashboard")
	const pathname = usePathname()
	const { user } = useAuthUser()

	// 检查用户是否有特定角色
	const hasRole = (requiredRoles?: string[]) => {
		// 如果没有指定角色要求，则所有用户都可以访问
		if (!requiredRoles || requiredRoles.length === 0) {
			return true
		}

		// 如果用户未登录或没有角色信息，则无法访问需要特定角色的菜单
		if (!user || !user.role) {
			return false
		}

		// 检查用户是否拥有所需角色之一
		return requiredRoles.includes(user.role)
	}

	const isActive = (href?: string) => {
		if (href === `/dashboard` && pathname === `/dashboard`) {
			return true
		}
		if (href !== `/dashboard` && pathname === href) {
			return true
		}
		return false
	}

	// 菜单分组
	const menuGroups: MenuGroup[] = [
		{
			title: "主要功能",
			items: [
				{
					name: t("dashboard"),
					key: "dashboard",
					href: `/dashboard`,
					icon: <LayoutDashboard className="w-4 h-4" />,
				},
				{
					name: t("websiteBuilder"),
					key: "websiteBuilder",
					href: `/dashboard/website-builder`,
					icon: <Building2 className="w-4 h-4" />,
				},
				{
					name: t("submission"),
					key: "submission",
					href: `/dashboard/submission`,
					icon: <Bot className="w-4 h-4" />,
				},
				{
					name: t("gameLibrary"),
					key: "gameLibrary",
					href: `/dashboard/game-library`,
					icon: <PlayCircle className="w-4 h-4" />,
				},
				{
					name: t("benefits"),
					key: "benefits",
					href: `/dashboard/benefits`,
					icon: <Gift className="w-4 h-4" />,
				},
			],
		},
		{
			title: "数据分析",
			items: [
				{
					name: t("subscriptionTrendKeyword"),
					key: "subscriptionTrendKeyword",
					href: `/dashboard/subscription-trend-keyword`,
					icon: <DollarSign className="w-4 h-4" />,
				},
				{
					name: t("keywordsTrendsResearch"),
					key: "keywordsTrendsResearch",
					href: `/dashboard/keywords-trends-research`,
					icon: <LineChart className="w-4 h-4" />,
					children: [
						{
							name: t("trendKeywordFavorite"),
							key: "trendKeywordFavorite",
							href: `/dashboard/trend-keyword/favorite`,
							icon: <Star className="w-4 h-4" />,
							benefits: [BenefitCode.DashboardTrendKeywordFavorite],
						},
						{
							name: t("trendKeywordPrivate"),
							key: "trendKeywordPrivate",
							href: `/dashboard/trend-keyword/private`,
							icon: <BookText className="w-4 h-4" />,
							benefits: [BenefitCode.DashboardTrendKeywordPrivate],
						},
						{
							name: t("trendKeywordPublic"),
							key: "trendKeywordPublic",
							href: `/dashboard/trend-keyword/public`,
							icon: <BookType className="w-4 h-4" />,
							benefits: [BenefitCode.DashboardTrendKeywordPublic],
						},
					],
				},
				{
					name: t("keywordsMediaResearch"),
					key: "keywordsMediaResearch",
					href: `/dashboard/keywords-media-research`,
					icon: <BarChart className="w-4 h-4" />,
				},
				{
					name: t("trendTaskAdmin"),
					key: "trendTaskAdmin",
					href: `/dashboard/trend-task-admin`,
					icon: <Timer className="w-4 h-4" />,
				},
			],
		},
		{
			title: "系统设置",
			roles: [UserRole.Admin],
			items: [
				{
					name: t("servers"),
					key: "servers",
					href: `/dashboard/servers`,
					icon: <Monitor className="w-4 h-4" />,
				},
				{
					name: t("cloudflareAccounts"),
					key: "cloudflareAccounts",
					href: `/dashboard/cloudflare-accounts`,
					icon: <Cloud className="w-4 h-4" />,
				},
				{
					name: t("tags"),
					key: "tags",
					href: `/dashboard/tags`,
					icon: <Tag className="w-4 h-4" />,
				},
				{
					name: t("users"),
					key: "users",
					href: `/dashboard/users`,
					icon: <Users className="w-4 h-4" />,
				},
				{
					name: "提示词管理",
					key: "prompts",
					href: `/dashboard/prompts`,
					icon: <Book className="w-4 h-4" />,
				},
				{
					name: "权益管理",
					key: "benefits-management",
					href: `/dashboard/benefits-management`,
					icon: <ShieldCheck className="w-4 h-4" />,
				},
				{
					name: "产品管理",
					key: "products-management",
					href: `/dashboard/products-management`,
					icon: <Package className="w-4 h-4" />,
				},
			],
		},
	]

	// Logo 组件
	const LogoComponent = () => (
		<div className="flex items-center justify-center py-5">
			<img src="/logo.png" alt="Logo" className="h-10 w-10" />
		</div>
	)

	return (
		<ShadcnSidebar
			side="left"
			variant="sidebar"
			className="bg-sidebar border-r border-sidebar-border scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 hover:scrollbar-thumb-gray-500"
		>
			{/* 顶部Logo */}
			<SidebarHeader>
				<LogoComponent />
			</SidebarHeader>

			{/* 菜单内容 */}
			<SidebarContent className="scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 hover:scrollbar-thumb-gray-500">
				{menuGroups
					.filter((item) => hasRole(item.roles))
					.map((group, groupIndex) => (
						<SidebarGroup key={groupIndex}>
							<SidebarGroupLabel>{group.title}</SidebarGroupLabel>
							<SidebarGroupContent>
								<SidebarMenu>
									{group.items
										.filter((item) => hasRole(item.roles))
										.map((item) => (
											<SidebarMenuItem key={item.key}>
												{item.children ? (
													<Collapsible
														defaultOpen={item.children.some((child) =>
															isActive(child.href),
														)}
														className="group/collapsible w-full"
													>
														<CollapsibleTrigger asChild>
															<SidebarMenuButton
																isActive={item.children.some((child) =>
																	isActive(child.href),
																)}
																tooltip={item.name}
																className="w-full justify-between"
															>
																<div className="flex items-center gap-2">
																	{item.icon}
																	<span>{item.name}</span>
																</div>
																<ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-180" />
															</SidebarMenuButton>
														</CollapsibleTrigger>
														<CollapsibleContent>
															<SidebarMenuSub>
																{item.children
																	.filter((child) => hasRole(child.roles))
																	.map((child) => (
																		<SidebarMenuSubItem key={child.key}>
																			<SidebarMenuSubButton
																				asChild
																				isActive={isActive(child.href)}
																			>
																				<Link href={child.href!}>
																					{child.icon}
																					<span>{child.name}</span>
																				</Link>
																			</SidebarMenuSubButton>
																		</SidebarMenuSubItem>
																	))}
															</SidebarMenuSub>
														</CollapsibleContent>
													</Collapsible>
												) : (
													<SidebarMenuButton
														asChild
														isActive={isActive(item.href)}
														tooltip={item.name}
													>
														<Link href={item.href!}>
															{item.icon}
															<span>{item.name}</span>
														</Link>
													</SidebarMenuButton>
												)}
											</SidebarMenuItem>
										))}
								</SidebarMenu>
							</SidebarGroupContent>
						</SidebarGroup>
					))}
			</SidebarContent>
		</ShadcnSidebar>
	)
}

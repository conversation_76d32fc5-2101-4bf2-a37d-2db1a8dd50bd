"use client"
// 解决 antd v5 和 react 19 的兼容性问题
import "@ant-design/v5-patch-for-react-19"
import { useEffect, useState } from "react"
import { useTranslations } from "next-intl"
import { Table, Card, Select, Input, DatePicker, Button, Tag, Spin } from "antd"
import { SearchOutlined, ReloadOutlined } from "@ant-design/icons"
import { format } from "date-fns"
import type { TableProps } from "antd"

const { RangePicker } = DatePicker

interface TrendTaskLog {
	id: string
	keywordId: string
	taskType: string
	triggerType: string
	status: string
	startTime: string
	endTime: string | null
	errorMsg: string | null
	response: any
	operatorId: string | null
	createdAt: string
	keyword: {
		word: string
	}
}

export default function TrendTaskAdminPage() {
	const t = useTranslations("trend-task-admin")
	const [logs, setLogs] = useState<TrendTaskLog[]>([])
	const [loading, setLoading] = useState(true)
	const [currentPage, setCurrentPage] = useState(1)
	const [total, setTotal] = useState(0)
	const [filters, setFilters] = useState({
		taskType: "",
		status: "",
		keywordId: "",
		dateRange: null as [Date, Date] | null,
	})

	const fetchLogs = async () => {
		try {
			setLoading(true)
			const params = new URLSearchParams({
				page: currentPage.toString(),
				limit: "10",
				...(filters.taskType && { taskType: filters.taskType }),
				...(filters.status && { status: filters.status }),
				...(filters.keywordId && { keywordId: filters.keywordId }),
				...(filters.dateRange && {
					startDate: filters.dateRange[0].toISOString(),
					endDate: filters.dateRange[1].toISOString(),
				}),
			})

			const response = await fetch(`/api/trend-task?${params}`)
			const data = await response.json()

			if (response.ok && data.data.data) {
				// 确保 data.data 是一个数组
				const logsData = Array.isArray(data.data.data) ? data.data.data : []
				console.log(logsData)
				setLogs(logsData)
				setTotal(data.data.total || 0)
			} else {
				setLogs([])
				setTotal(0)
			}
		} catch (error) {
			console.error("Error fetching logs:", error)
			setLogs([])
			setTotal(0)
		} finally {
			setLoading(false)
		}
	}

	useEffect(() => {
		fetchLogs()
	}, [currentPage, filters])

	const getStatusTag = (status: string) => {
		const statusMap: Record<string, { color: string; label: string }> = {
			PENDING: { color: "default", label: t("status.pending") },
			RUNNING: { color: "processing", label: t("status.running") },
			SUCCESS: { color: "success", label: t("status.success") },
			FAILED: { color: "error", label: t("status.failed") },
		}
		const { color, label } = statusMap[status] || {
			color: "default",
			label: status,
		}
		return <Tag color={color}>{label}</Tag>
	}

	const columns: TableProps<TrendTaskLog>["columns"] = [
		{
			title: t("table.keyword"),
			dataIndex: ["keyword", "word"],
			key: "keyword",
			render: (text) => text || "-",
		},
		{
			title: t("table.taskType"),
			dataIndex: "taskType",
			key: "taskType",
			render: (text) => t(`taskTypes.${text.toLowerCase()}`),
		},
		{
			title: t("table.triggerType"),
			dataIndex: "triggerType",
			key: "triggerType",
			render: (text) => t(`triggerTypes.${text.toLowerCase()}`),
		},
		{
			title: t("table.status"),
			dataIndex: "status",
			key: "status",
			render: (text) => getStatusTag(text),
		},
		{
			title: t("table.startTime"),
			dataIndex: "startTime",
			key: "startTime",
			render: (text) => format(new Date(text), "yyyy-MM-dd HH:mm:ss"),
		},
		{
			title: t("table.endTime"),
			dataIndex: "endTime",
			key: "endTime",
			render: (text) =>
				text ? format(new Date(text), "yyyy-MM-dd HH:mm:ss") : "-",
		},
		{
			title: t("table.error"),
			dataIndex: "errorMsg",
			key: "errorMsg",
			render: (text) => text || "-",
			ellipsis: true,
		},
	]

	return (
		<div className="p-6 min-h-[calc(100vh-6.5rem)] container mx-auto bg-white rounded-md m-5">
			<Card title={t("title")}>
				<div className="mb-6">
					<div className="flex flex-wrap gap-4 mb-4">
						<Select
							placeholder={t("filters.taskType")}
							style={{ width: 200 }}
							value={filters.taskType}
							onChange={(value) => setFilters({ ...filters, taskType: value })}
							options={[
								{ value: "TREND", label: t("taskTypes.trend") },
								{ value: "AI", label: t("taskTypes.ai") },
							]}
						/>

						<Select
							placeholder={t("filters.status")}
							style={{ width: 200 }}
							value={filters.status}
							onChange={(value) => setFilters({ ...filters, status: value })}
							options={[
								{ value: "PENDING", label: t("status.pending") },
								{ value: "RUNNING", label: t("status.running") },
								{ value: "SUCCESS", label: t("status.success") },
								{ value: "FAILED", label: t("status.failed") },
							]}
						/>

						<Input
							placeholder={t("filters.keywordId")}
							style={{ width: 200 }}
							value={filters.keywordId}
							onChange={(e) =>
								setFilters({ ...filters, keywordId: e.target.value })
							}
						/>

						<RangePicker
							showTime
							format="YYYY-MM-DDD HH:mm:ss"
							onChange={(dates) =>
								setFilters({
									...filters,
									dateRange: dates as [Date, Date] | null,
								})
							}
						/>

						<Button
							type="primary"
							icon={<SearchOutlined />}
							onClick={() => {
								setCurrentPage(1)
								fetchLogs()
							}}
						>
							{t("search")}
						</Button>

						<Button
							icon={<ReloadOutlined />}
							onClick={() => {
								setFilters({
									taskType: "",
									status: "",
									keywordId: "",
									dateRange: null,
								})
								setCurrentPage(1)
							}}
						>
							{t("reset")}
						</Button>
					</div>
				</div>

				<Table
					columns={columns}
					dataSource={logs}
					rowKey="id"
					loading={loading}
					pagination={{
						current: currentPage,
						total,
						pageSize: 10,
						onChange: (page) => setCurrentPage(page),
					}}
					scroll={{ y: "calc(100vh - 11rem)" }}
				/>
			</Card>
		</div>
	)
}

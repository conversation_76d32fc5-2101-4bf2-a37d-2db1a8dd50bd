import { hasValidBenefit } from "@repo/web/lib/services/BenefitService"
import { BenefitCode } from "@repo/shared-types"
import { authUser } from "@repo/auth/server"
import { NotLogin, NoPermission } from "@/app/not-found"
import { Tooltip as AntdTooltip } from "antd"
import { QuestionCircleOutlined } from "@ant-design/icons"
import TrendKeywordListPage from "../TrendKeywordTabsPage"

export default async function Page() {
	const user = await authUser()
	if (!user) {
		return NotLogin()
	}
	const [hasPermission, error] = await hasValidBenefit({
		userId: user.id,
		benefitCode: BenefitCode.DashboardTrendKeywordPublic,
	})
	if (!hasPermission) {
		return NoPermission({ message: error })
	}
	const tabs = [
		{
			key: "public",
			label: (
				<span>
					公共词库
					<AntdTooltip
						title={
							<div className="space-y-2">
								<p>公共词库功能说明：</p>
								<ul className="list-disc pl-4">
									<li>查看和管理所有用户共享的关键词</li>
									<li>可以将关键词添加到私有库</li>
									<li>支持收藏感兴趣的关键词</li>
									<li>管理员可以删除公共词库中的关键词</li>
								</ul>
							</div>
						}
					>
						<QuestionCircleOutlined className="ml-2 text-gray-400 hover:text-gray-600" />
					</AntdTooltip>
				</span>
			),
		},
		{
			key: "public_root",
			label: (
				<span>
					公共词根管理
					<AntdTooltip
						title={
							<div className="space-y-2">
								<p>公共词根管理说明：</p>
								<ul className="list-disc pl-4">
									<li>管理所有用户共享的词根</li>
									<li>词根用于组织和管理相关关键词</li>
									<li>管理员可以添加和删除公共词根</li>
									<li>普通用户可以查看和使用公共词根</li>
								</ul>
							</div>
						}
					>
						<QuestionCircleOutlined className="ml-2 text-gray-400 hover:text-gray-600" />
					</AntdTooltip>
				</span>
			),
		},
	]
	return <TrendKeywordListPage user={user} tabs={tabs} defaultTab="public" />
}

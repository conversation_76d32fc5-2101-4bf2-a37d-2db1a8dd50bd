"use client"

import { useEffect, useState, useCallback, useMemo } from "react"
import { useTranslations } from "next-intl"
import {
	Table,
	Input,
	Tabs,
	Popconfirm,
	Tooltip as AntdTooltip,
	Checkbox,
} from "antd"
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>nt,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Button,
	useDisclosure,
} from "@heroui/react"
import { AuthUser } from "@repo/auth"
import { UserRole } from "@/lib/consts"
import { fetchDelete, fetchGet, fetchPost } from "@repo/utils/react"
import { toast } from "sonner"

import { RelativeTime } from "@repo/utils"
import { Trash2, Search, TrendingUp, Star, StarOff } from "lucide-react"
import { useRouter } from "next/navigation"
import { Line } from "react-chartjs-2"
import {
	Chart as ChartJS,
	CategoryScale,
	LinearScale,
	PointElement,
	LineElement,
	Title,
	Tooltip,
	Filler,
	Legend,
	ChartOptions,
} from "chart.js"

// 注册Chart.js组件
ChartJS.register(
	CategoryScale,
	LinearScale,
	PointElement,
	LineElement,
	Title,
	Tooltip,
	Filler,
	Legend,
)

interface Keyword {
	id: string
	word: string
	rootId: string
	rootWord?: string
	updatedAt: string
	status?: string
	trendScore?: number
	trendData?: any[]
}

interface TrendKeywordTabsPageProps {
	user: AuthUser
	tabs: any[]
	defaultTab: string
}

export default function TrendKeywordTabsPage({
	user,
	tabs,
	defaultTab,
}: TrendKeywordTabsPageProps) {
	const router = useRouter()
	const visibleTabs = useMemo(
		() => tabs.filter((tab) => !tab.adminOnly || user.role === UserRole.Admin),
		[user.role, tabs],
	)
	const t = useTranslations("trend_keyword_list")
	const [tab, setTab] = useState(defaultTab)
	const [search, setSearch] = useState("")
	const [page, setPage] = useState(1)
	const [pageSize, setPageSize] = useState(20)
	const [sortBy, setSortBy] = useState("updatedAt")
	const [order, setOrder] = useState("desc")
	const [loading, setLoading] = useState(false)
	const [data, setData] = useState<any[]>([])
	const [total, setTotal] = useState(0)
	const {
		isOpen: isOpenAdd,
		onOpen: onOpenAdd,
		onOpenChange: onOpenChangeAdd,
		onClose: onCloseAdd,
	} = useDisclosure()
	const [newWords, setNewWords] = useState("")
	const [hasTrendData, setHasTrendData] = useState(false)

	// 使用 react-chartjs-2 实现趋势折线图
	function TrendLineChart({
		trendData,
		keyword,
		trendLastUpdate,
	}: { trendData: any[]; keyword: string; trendLastUpdate: string }) {
		// 数据处理
		const chartData = useMemo(() => {
			if (!Array.isArray(trendData) || trendData.length === 0)
				return { labels: [], datasets: [] }
			if (typeof trendData[0] === "object" && trendData[0].date) {
				const labels = trendData.map((item: any) => {
					try {
						const date = new Date(item.date)
						return date.toISOString().slice(0, 10)
					} catch {
						return item.date
					}
				})
				const values = trendData.map(
					(item: any) => item[keyword] ?? item.value ?? 0,
				)
				return {
					labels,
					datasets: [
						{
							data: values,
							borderColor: "#8884d8",
							backgroundColor: "rgba(136,132,216,0.08)",
							pointRadius: 0,
							fill: true,
							tension: 0.4,
						},
					],
				}
			}
			// 简单数值数组
			return {
				labels: trendData.map((_: any, i: number) => i + 1),
				datasets: [
					{
						data: trendData.map((v: any) =>
							typeof v === "number" ? v : v.value || 0,
						),
						borderColor: "#8884d8",
						backgroundColor: "rgba(136,132,216,0.08)",
						pointRadius: 0,
						fill: true,
						tension: 0.4,
					},
				],
			}
		}, [trendData, keyword])

		const options = useMemo(
			() => ({
				responsive: true,
				maintainAspectRatio: false,
				plugins: {
					legend: { display: false },
					tooltip: { enabled: false },
				},
				elements: {
					line: { borderWidth: 2 },
				},
				scales: {
					x: {
						type: "category" as const,
						display: false,
						grid: { display: false },
						ticks: { display: false },
						border: { display: false },
					},
					y: {
						type: "linear" as const,
						display: false,
						grid: { display: false },
						ticks: { display: false },
						border: { display: false },
					},
				},
			}),
			[],
		)

		if (!Array.isArray(trendData) || trendData.length === 0) {
			return (
				<div
					className="flex flex-col items-center justify-center"
					style={{ height: 54, width: 120 }}
				>
					<div className="text-xs text-gray-400">
						{trendLastUpdate ? "等待刷新" : "趋势数据为空"}
					</div>
					{trendLastUpdate && (
						<div className="text-xs text-gray-500 mt-1">
							更新于 <RelativeTime time={trendLastUpdate} />
						</div>
					)}
				</div>
			)
		}

		return (
			<div className="flex flex-col items-center">
				<div style={{ height: 54, width: 120 }}>
					<Line data={chartData} options={options} />
				</div>
				{trendLastUpdate && (
					<div className="text-xs text-gray-500 mt-1">
						更新于 <RelativeTime time={trendLastUpdate} />
					</div>
				)}
			</div>
		)
	}

	// 表格字段定义（不同tab有不同字段）
	const keywordColumns = useMemo(() => {
		const baseColumns: any[] = [
			{
				title: "关键词",
				dataIndex: "word",
				key: "word",
				render: (text: string, record: any) => {
					const endDate = new Date()
					const startDate = new Date(endDate)
					startDate.setDate(startDate.getDate() - 14)
					const dateRange = `${startDate.toISOString().split("T")[0]} ${endDate.toISOString().split("T")[0]}`

					return (
						<div className="group relative w-full h-full flex items-center">
							<span
								className="cursor-pointer hover:text-blue-500 hover:underline flex-1"
								onClick={() =>
									router.push(`/dashboard/trend-keyword/detail?id=${record.id}`)
								}
							>
								{text}
							</span>
							<div className="absolute right-0 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
								<a
									href={`https://trends.google.com/trends/explore?q=${encodeURIComponent(text)}&date=${encodeURIComponent(dateRange)}`}
									target="_blank"
									rel="noopener noreferrer"
									className="text-blue-500 hover:text-blue-700"
								>
									<TrendingUp className="size-4" />
								</a>
								<a
									href={`https://www.google.com/search?q=${encodeURIComponent(text)}`}
									target="_blank"
									rel="noopener noreferrer"
									className="text-blue-500 hover:text-blue-700"
								>
									<Search className="size-4" />
								</a>
							</div>
						</div>
					)
				},
			},
			{
				title: "14天趋势图",
				dataIndex: "trendLastUpdate",
				key: "trendLastUpdate",
				sorter: true,
				render: (_: any, record: any) => (
					<TrendLineChart
						trendData={record.trendData}
						keyword={record.word}
						trendLastUpdate={record.trendLastUpdate}
					/>
				),
			},
			{ title: "所属词根", dataIndex: "parentWord", key: "parentWord" },
			{
				title: "类型",
				dataIndex: "type",
				key: "type",
				render: (t: string) => (t === "ROOT" ? "词根" : "关联词"),
			},
			{
				title: "搜索量上升",
				dataIndex: "trendScore",
				key: "trendScore",
				render: (score: string) =>
					score && typeof score === "number" ? `${score}%` : `${score ?? "-"}`,
			},
			{ title: "新词热度", dataIndex: "aiScore", key: "aiScore", sorter: true },
			{
				title: "AI标签",
				dataIndex: "aiTagList",
				key: "aiTagList",
				render: (tags: string[]) =>
					tags && tags.length > 0 ? (
						<div>
							{tags.map((tag) => (
								<span key={tag} className="tag">
									{tag}
								</span>
							))}
						</div>
					) : (
						"-"
					),
			},
			{
				title: "创建时间",
				dataIndex: "createdAt",
				key: "createdAt",
				sorter: true,
				render: (v: string) => (v ? <RelativeTime time={v} /> : "-"),
			},
			{
				title: "更新时间",
				dataIndex: "updatedAt",
				key: "updatedAt",
				sorter: true,
				render: (v: string) => (v ? <RelativeTime time={v} /> : "-"),
			},
		]

		// 只在 public、private 和 favorite tab 中添加收藏功能
		if (tab === "public" || tab === "private" || tab === "favorite") {
			baseColumns.push({
				title: "收藏",
				dataIndex: "isFavorite",
				key: "isFavorite",
				render: (isFavorite: boolean, record: any) => (
					<span
						className="cursor-pointer"
						onClick={() => handleToggleFavorite(record)}
					>
						{isFavorite ? (
							<Star className="size-5 text-yellow-500 fill-yellow-500" />
						) : (
							<StarOff className="size-5 text-gray-400" />
						)}
					</span>
				),
			})
		}

		baseColumns.push({
			title: "操作",
			key: "action",
			dataIndex: "action",
			render: (_: any, record: any) => (
				<div className="inline-flex gap-3 items-center">
					<span className="cursor-pointer inline-block">
						<AntdTooltip title="删除">
							<Popconfirm
								title="确定删除该词根？"
								onConfirm={() => handleDelete(record)}
							>
								<Trash2 className="text-red-500 size-5" />
							</Popconfirm>
						</AntdTooltip>
					</span>
				</div>
			),
		})

		return baseColumns
	}, [tab, router])

	// 词根管理tab字段
	const rootColumns = [
		{ title: "词根", dataIndex: "word", key: "word" },
		{
			title: "创建时间",
			dataIndex: "createdAt",
			key: "createdAt",
			render: (v: string) => (v ? <RelativeTime time={v} /> : "-"),
		},
		{
			title: "更新时间",
			dataIndex: "updatedAt",
			key: "updatedAt",
			render: (v: string) => (v ? <RelativeTime time={v} /> : "-"),
		},
		{
			title: "操作",
			key: "action",
			render: (_: any, record: any) => (
				<span className="cursor-pointer inline-block">
					<Popconfirm
						title="确定删除该词根？"
						onConfirm={() => handleDelete(record)}
					>
						<Trash2 className="text-red-500 size-5" />
					</Popconfirm>
				</span>
			),
		},
	]

	// 数据加载
	const reloadData = useCallback(() => {
		setLoading(true)
		let url = ""
		if (tab === "public") {
			url = `/api/trend-keyword/public-list?page=${page}&pageSize=${pageSize}&keyword=${encodeURIComponent(search)}&orderBy=${sortBy}&order=${order}&hasTrendData=${hasTrendData}`
		} else if (tab === "private") {
			url = `/api/trend-keyword/private-list?page=${page}&pageSize=${pageSize}&keyword=${encodeURIComponent(search)}&orderBy=${sortBy}&order=${order}&hasTrendData=${hasTrendData}`
		} else if (tab === "favorite") {
			url = `/api/trend-keyword/favorite?page=${page}&pageSize=${pageSize}&keyword=${encodeURIComponent(search)}&orderBy=${sortBy}&order=${order}&hasTrendData=${hasTrendData}`
		} else if (tab === "public_root") {
			url = `/api/trend-keyword/public-roots?page=${page}&pageSize=${pageSize}&keyword=${encodeURIComponent(search)}&orderBy=${sortBy}&order=${order}&hasTrendData=${hasTrendData}`
		} else if (tab === "private_root") {
			url = `/api/trend-keyword/private-roots?page=${page}&pageSize=${pageSize}&keyword=${encodeURIComponent(search)}&orderBy=${sortBy}&order=${order}&hasTrendData=${hasTrendData}`
		}
		fetchGet(url)
			.then((res) => {
				setData(res.data || [])
				setTotal(res.total || 0)
			})
			.finally(() => setLoading(false))
	}, [tab, search, page, pageSize, sortBy, order, hasTrendData])

	useEffect(() => {
		reloadData()
	}, [tab, search, page, pageSize, sortBy, order, hasTrendData])

	const handleAdd = async () => {
		const words = newWords
			.split("\n")
			.map((w) => w.trim())
			.filter(Boolean)
		if (words.length === 0) return
		setLoading(true)
		try {
			await fetchPost(`/api/trend-keyword/batch`, {
				words,
				...(tab === "public_root" && user.role === UserRole.Admin
					? { isGlobal: true }
					: {}),
			})
			setNewWords("")
			onCloseAdd()
			setPage(1)
			reloadData()
		} catch (e: any) {
			toast.error(`批量新增失败: ${e}`)
		} finally {
			setLoading(false)
		}
	}

	const handleDelete = async (record: any) => {
		setLoading(true)
		try {
			await fetchDelete(`/api/trend-keyword/${record.id}`)
			toast.success("删除成功")
			reloadData()
		} catch (e: any) {
			toast.error(e?.message || "删除失败")
		} finally {
			setLoading(false)
		}
	}

	const handleTableChange = (pagination: any, filters: any, sorter: any) => {
		if (sorter?.field) {
			setSortBy(sorter.field)
			setOrder(sorter.order === "ascend" ? "asc" : "desc")
		}
	}

	const handleToggleFavorite = async (record: any) => {
		if (loading) return
		setLoading(true)
		try {
			await fetchPost(`/api/trend-keyword/toggle-favorite/${record.id}`, {
				isFavorite: !record.isFavorite,
			})
			toast.success(record.isFavorite ? "已取消收藏" : "已添加收藏")
			reloadData()
		} catch (e: any) {
			toast.error(
				e?.message || (record.isFavorite ? "取消收藏失败" : "收藏失败"),
			)
		} finally {
			setLoading(false)
		}
	}

	return (
		<div className="container mx-auto p-5 my-5 bg-white rounded-md min-h-[calc(100vh-6.5rem)]">
			<Tabs
				activeKey={tab}
				onChange={(key) => {
					setTab(key)
					setPage(1)
					setSearch("")
					setHasTrendData(false)
				}}
				items={visibleTabs}
			/>
			<div className="flex mb-4 gap-2 items-center">
				<Input.Search
					value={search}
					onChange={(e) => setSearch(e.target.value)}
					placeholder={t("trend_keyword_list_table_word")}
					style={{ width: 240 }}
					allowClear
				/>
				<Checkbox
					checked={hasTrendData}
					onChange={(e) => {
						setHasTrendData(e.target.checked)
						setPage(1)
					}}
				>
					有趋势数据
				</Checkbox>
				{(tab === "private" ||
					tab === "private_root" ||
					(tab === "public_root" && user.role === UserRole.Admin)) && (
					<Button color="primary" onPress={onOpenAdd}>
						{t("trend_keyword_list_add_batch")}
					</Button>
				)}
			</div>
			{/* 公共/自定义词库树状结构 */}
			{(tab === "public" || tab === "private") && (
				<Table
					columns={keywordColumns}
					dataSource={data}
					rowKey="id"
					loading={loading}
					expandable={{ childrenColumnName: "children" }}
					pagination={{
						total,
						pageSize,
						current: page,
						onChange: setPage,
						showTotal: (total) => `共 ${total} 条数据`,
					}}
					onChange={handleTableChange}
					rowClassName={(record, index) =>
						index % 2 === 0 ? "bg-white" : "bg-gray-50"
					}
					scroll={{ y: "h-full" }}
				/>
			)}
			{/* 我的收藏 */}
			{tab === "favorite" && (
				<Table
					columns={keywordColumns}
					dataSource={data}
					rowKey="id"
					loading={loading}
					pagination={{
						total,
						pageSize,
						current: page,
						onChange: setPage,
						showTotal: (total) => `共 ${total} 条数据`,
					}}
					onChange={handleTableChange}
					rowClassName={(record, index) =>
						index % 2 === 0 ? "bg-white" : "bg-gray-50"
					}
					scroll={{ y: "calc(80vh - 11rem)" }}
				/>
			)}
			{/* 公共词根管理 */}
			{tab === "public_root" && (
				<Table
					columns={rootColumns}
					dataSource={data}
					rowKey="id"
					loading={loading}
					pagination={{
						total,
						pageSize,
						current: page,
						onChange: setPage,
						showTotal: (total) => `共 ${total} 条数据`,
					}}
					onChange={handleTableChange}
					rowClassName={(record, index) =>
						index % 2 === 0 ? "bg-white" : "bg-gray-50"
					}
					scroll={{ y: "h-full" }}
					className="scrollbar-thumb-gray-300 scrollbar-track-gray-100 scrollbar-thin hover:scrollbar-thumb-gray-400"
				/>
			)}
			{/* 私有词根管理 */}
			{tab === "private_root" && (
				<Table
					columns={rootColumns}
					dataSource={data}
					rowKey="id"
					loading={loading}
					pagination={{
						total,
						pageSize,
						current: page,
						onChange: setPage,
						showTotal: (total) => `共 ${total} 条数据`,
					}}
					onChange={handleTableChange}
					rowClassName={(record, index) =>
						index % 2 === 0 ? "bg-white" : "bg-gray-50"
					}
					scroll={{ y: "calc(80vh - 11rem)" }}
					className="scrollbar-thumb-gray-300 scrollbar-track-gray-100 scrollbar-thin hover:scrollbar-thumb-gray-400"
				/>
			)}
			{/* 新增词根弹窗 */}
			<Modal isOpen={isOpenAdd} onOpenChange={onOpenChangeAdd}>
				<ModalContent>
					<ModalHeader>{t("trend_keyword_list_add_batch_title")}</ModalHeader>
					<div className="p-4  border-gray-300 rounded">
						<Input.TextArea
							rows={6}
							value={newWords}
							onChange={(e) => setNewWords(e.target.value)}
							placeholder={t("trend_keyword_list_add_batch_placeholder")}
							autoFocus
						/>
					</div>
					<ModalFooter>
						<Button onPress={onCloseAdd} variant="ghost">
							{t("trend_keyword_list_cancel")}
						</Button>
						<Button color="primary" onPress={handleAdd} isLoading={loading}>
							{t("trend_keyword_list_confirm")}
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>
		</div>
	)
}

"use client"

import { useTranslations } from "next-intl"
import {
	Input,
	Textarea,
	Button,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
	Alert,
	AlertTitle,
	AlertDescription,
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@repo/ui/components"
import { AlertCircle, Loader2 } from "lucide-react"
import {
	useGameMetadataForm,
	MetadataFormData,
} from "../../hooks/useGameMetadataForm"
import TranslationSwitcher from "@/lib/components/translation/TranslationSwitcher"
import { useFormWithTranslation, TranslationConfirmDialog } from "@/lib/components/translation"
import { ProjectLanguage } from "@repo/shared-types"

interface MetadataTabProps {
	projectId: string
	gameId: string
	defaultLanguage?: ProjectLanguage
	languages?: { code: string; name: string }[]
}

export default function MetadataTab({
	projectId,
	gameId,
	defaultLanguage = ProjectLanguage.EN,
	languages = [],
}: MetadataTabProps) {
	const t = useTranslations("Games")

	const {
		form,
		isLoading,
		error,
		handleSubmit: originalHandleSubmit,
		isSaving,
		metadata,
		refreshMetadata,
	} = useGameMetadataForm({
		projectId,
		gameId,
	})

	// 集成翻译确认功能
	const { handleSubmit: handleSubmitWithTranslation, translationDialogProps } = useFormWithTranslation(
		async (data: MetadataFormData, options?: { isTranslation?: boolean }) => {
			return originalHandleSubmit(data, options)
		},
		{
			projectId,
			contentType: "game-metadata",
			confirmOptions: {
				title: "发起游戏元数据翻译任务",
				description: "是否同时将游戏元数据翻译到其他语言？",
				confirmText: "发起翻译",
				cancelText: "仅保存",
			}
		}
	)



	if (isLoading) {
		return (
			<div className="flex items-center justify-center h-64">
				<Loader2 className="w-8 h-8 animate-spin text-primary" />
				<span className="ml-2 text-lg">加载中...</span>
			</div>
		)
	}

	if (error) {
		return (
			<Alert variant="destructive">
				<AlertCircle className="h-4 w-4" />
				<AlertTitle>{t("error")}</AlertTitle>
				<AlertDescription>{t("loadMetadataError")}</AlertDescription>
			</Alert>
		)
	}

	return (
		<>
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit((data: MetadataFormData) =>
						handleSubmitWithTranslation(data),
					)}
					className="space-y-8"
				>
				<Card>
					<CardHeader>
						<div className="flex justify-between items-start mb-4">
							<div>
								<CardTitle>{t("metaInformation")}</CardTitle>
								<CardDescription>
									{t("metaInformationDescription")}
								</CardDescription>
							</div>
							<TranslationSwitcher
								onLanguageChange={(_code) => {
									// 当语言切换时刷新元数据
									refreshMetadata()
								}}
							/>
						</div>
					</CardHeader>
					<CardContent className="space-y-4">
						<FormField
							control={form.control}
							name="title"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("metaTitle")}</FormLabel>
									<FormControl>
										<Input
											placeholder={t("enterMetaTitle")}
											{...field}
											maxLength={60}
										/>
									</FormControl>
									<FormDescription>
										{field.value?.length || 0}/60 {t("characters")}
										{field.value?.length > 60 && (
											<span className="text-yellow-500">
												{" "}
												({t("recommendedMax")} 60)
											</span>
										)}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

					<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("metaName")}</FormLabel>
									<FormControl>
										<Input
											placeholder={t("enterMetaName")}
											{...field}
											maxLength={60}
										/>
									</FormControl>
									<FormDescription>
										{field?.value?.length || 0}/60 {t("characters")}
										{field.value && field.value?.length > 60 && (
											<span className="text-yellow-500">
												{" "}
												({t("recommendedMax")} 60)
											</span>
										)}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="slogan"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("metaSlogan")}</FormLabel>
									<FormControl>
										<Input
											placeholder={t("enterMetaSlogan")}
											{...field}
											maxLength={100}
										/>
									</FormControl>
									<FormDescription>
										{field?.value?.length || 0}/100 {t("characters")}
										{field.value && field.value?.length > 100 && (
											<span className="text-yellow-500">
												{" "}
												({t("recommendedMax")} 100)
											</span>
										)}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("metaDescription")}</FormLabel>
									<FormControl>
										<Textarea
											placeholder={t("enterMetaDescription")}
											{...field}
											rows={3}
											maxLength={160}
										/>
									</FormControl>
									<FormDescription>
										{field.value?.length || 0}/200 {t("characters")}
										{field.value?.length > 160 && (
											<span className="text-yellow-500">
												{" "}
												({t("recommendedMax")} 160)
											</span>
										)}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					</CardContent>
				</Card>

				{/* <Card>
					<CardHeader>
						<CardTitle>{t("socialMediaSharing")}</CardTitle>
						<CardDescription>
							{t("socialMediaSharingDescription")}
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-6">
						<div className="space-y-4">
							<h3 className="text-lg font-medium">{t("openGraph")}</h3>

							<FormField
								control={form.control}
								name="ogTitle"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("ogTitle")}</FormLabel>
										<FormControl>
											<Input placeholder={t("enterOgTitle")} {...field} />
										</FormControl>
										<FormDescription>{t("ogTitleDescription")}</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="ogDescription"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("ogDescription")}</FormLabel>
										<FormControl>
											<Textarea
												placeholder={t("enterOgDescription")}
												{...field}
												rows={2}
											/>
										</FormControl>
										<FormDescription>{t("ogDescriptionInfo")}</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="ogImage"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("ogImage")}</FormLabel>
										<FormControl>
											<Input placeholder={t("enterOgImageUrl")} {...field} />
										</FormControl>
										<FormDescription>{t("ogImageDescription")}</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="space-y-4 pt-4 border-t">
							<h3 className="text-lg font-medium">{t("twitter")}</h3>

							<FormField
								control={form.control}
								name="twitterCard"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("twitterCard")}</FormLabel>
										<FormControl>
											<Input placeholder={t("enterTwitterCard")} {...field} />
										</FormControl>
										<FormDescription>
											{t("twitterCardDescription")}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="twitterTitle"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("twitterTitle")}</FormLabel>
										<FormControl>
											<Input placeholder={t("enterTwitterTitle")} {...field} />
										</FormControl>
										<FormDescription>
											{t("twitterTitleDescription")}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="twitterDescription"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("twitterDescription")}</FormLabel>
										<FormControl>
											<Textarea
												placeholder={t("enterTwitterDescription")}
												{...field}
												rows={2}
											/>
										</FormControl>
										<FormDescription>
											{t("twitterDescriptionInfo")}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="twitterImage"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("twitterImage")}</FormLabel>
										<FormControl>
											<Input
												placeholder={t("enterTwitterImageUrl")}
												{...field}
											/>
										</FormControl>
										<FormDescription>
											{t("twitterImageDescription")}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</CardContent>
				</Card> */}

				<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
					<div className="max-w-6xl mx-auto flex justify-end w-full">
						<Button
							type="reset"
							variant="outline"
							disabled={isSaving}
							className="mr-3"
						>
							{t("reset")}
						</Button>
						<Button type="submit" loading={isSaving}>
							{isSaving ? t("saving") : t("saveSettings")}
						</Button>
					</div>
				</div>
			</form>
		</Form>

		{/* 翻译确认对话框 */}
		<TranslationConfirmDialog {...translationDialogProps} />
	</>
	)
}

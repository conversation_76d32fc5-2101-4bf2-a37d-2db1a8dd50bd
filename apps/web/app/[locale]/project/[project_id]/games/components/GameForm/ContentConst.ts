import { ProjectLanguage, GameDetailContentType } from "@repo/shared-types"

export const MODULE_TRANSLATIONS = {
	// 游戏概述
	gameOverview: {
		name: "游戏概述",
		title: {
			en: "Game Overview",
			de: "Spielübersicht",
			fr: "Aper<PERSON>u du jeu",
			ja: "ゲーム概要",
			ko: "게임 개요",
			"zh-TW": "遊戲概述",
			"es-ES": "Resumen del juego",
			it: "Panoramica del gioco",
			nl: "Speloverzicht",
			"pt-PT": "Visão geral do jogo",
		},
		description: "介绍游戏的整体概况和基本信息",
	},
	// 游戏玩法
	howToPlay: {
		name: "游戏玩法",
		title: {
			en: "How to Play",
			de: "Wie man spielt",
			fr: "Comment jouer",
			ja: "遊び方",
			ko: "플레이 방법",
			"zh-TW": "遊戲玩法",
			"es-ES": "Cómo jugar",
			it: "Come giocare",
			nl: "Hoe te spelen",
			"pt-PT": "Como jogar",
		},
		description: "详细介绍游戏的玩法规则和操作方式",
	},
	// 核心特色
	coreFeatures: {
		name: "核心特色",
		title: {
			en: "Core Features",
			de: "Kernfunktionen",
			fr: "Fonctionnalités principales",
			ja: "コア機能",
			ko: "핵심 기능",
			"zh-TW": "核心特色",
			"es-ES": "Características principales",
			it: "Caratteristiche principali",
			nl: "Kernfuncties",
			"pt-PT": "Características principais",
		},
		description: "介绍游戏的核心特色和亮点功能",
	},
	// 游戏技巧
	tipsAndTricks: {
		name: "游戏技巧",
		title: {
			en: "Tips & Tricks",
			de: "Tipps & Tricks",
			fr: "Conseils et astuces",
			ja: "コツとテクニック",
			ko: "팁과 요령",
			"zh-TW": "遊戲技巧",
			"es-ES": "Consejos y trucos",
			it: "Suggerimenti e trucchi",
			nl: "Tips & Tricks",
			"pt-PT": "Dicas e truques",
		},
		description: "提供游戏的实用技巧和攻略建议",
	},
	// 游戏截图
	screenshots: {
		name: "游戏截图",
		title: {
			en: "Screenshots",
			de: "Screenshots",
			fr: "Captures d'écran",
			ja: "スクリーンショット",
			ko: "스크린샷",
			"zh-TW": "遊戲截圖",
			"es-ES": "Capturas de pantalla",
			it: "Screenshot",
			nl: "Screenshots",
			"pt-PT": "Capturas de ecrã",
		},
		description: "展示游戏的精美截图和画面效果",
	},
	// 推荐理由
	whyPlayHere: {
		name: "推荐理由",
		title: {
			en: "Why Play Here?",
			de: "Warum hier spielen?",
			fr: "Pourquoi jouer ici?",
			ja: "なぜここでプレイ？",
			ko: "왜 여기서 플레이?",
			"zh-TW": "推薦理由",
			"es-ES": "¿Por qué jugar aquí?",
			it: "Perché giocare qui?",
			nl: "Waarom hier spelen?",
			"pt-PT": "Por que jogar aqui?",
		},
		description: "说明选择在此平台游戏的优势和理由",
	},
	// 平台信息
	platformInfo: {
		name: "平台信息",
		title: {
			en: "Platform Info",
			de: "Plattform-Info",
			fr: "Informations sur la plateforme",
			ja: "プラットフォーム情報",
			ko: "플랫폼 정보",
			"zh-TW": "平台資訊",
			"es-ES": "Información de la plataforma",
			it: "Informazioni sulla piattaforma",
			nl: "Platforminformatie",
			"pt-PT": "Informações da plataforma",
		},
		description: "介绍平台的特色功能和服务信息",
	},
	// 常见问题
	faq: {
		name: "常见问题",
		title: {
			en: "FAQ",
			de: "FAQ",
			fr: "FAQ",
			ja: "よくある質問",
			ko: "자주 묻는 질문",
			"zh-TW": "常見問題",
			"es-ES": "Preguntas frecuentes",
			it: "FAQ",
			nl: "Veelgestelde vragen",
			"pt-PT": "Perguntas frequentes",
		},
		description: "回答关于游戏的常见问题和疑问",
	},
	// 热门视频
	popularVideos: {
		name: "热门视频",
		title: {
			en: "Popular Videos",
			de: "Beliebte Videos",
			fr: "Vidéos populaires",
			ja: "人気動画",
			ko: "인기 비디오",
			"zh-TW": "熱門影片",
			"es-ES": "Videos populares",
			it: "Video popolari",
			nl: "Populaire video's",
			"pt-PT": "Vídeos populares",
		},
		description: "展示游戏相关的热门视频内容",
	},
} as const

export const availableModules = (currentLanguage: ProjectLanguage) => {
	return [
		{
			tabId: "gameOverview",
			name: MODULE_TRANSLATIONS.gameOverview.name,
			title:
				MODULE_TRANSLATIONS.gameOverview.title[currentLanguage] ||
				MODULE_TRANSLATIONS.gameOverview.title.en,
			icon: "info",
			description: MODULE_TRANSLATIONS.gameOverview.description,
		},
		{
			tabId: "howToPlay",
			name: MODULE_TRANSLATIONS.howToPlay.name,
			title:
				MODULE_TRANSLATIONS.howToPlay.title[currentLanguage] ||
				MODULE_TRANSLATIONS.howToPlay.title.en,
			icon: "gamepad",
			description: MODULE_TRANSLATIONS.howToPlay.description,
		},
		{
			tabId: "coreFeatures",
			name: MODULE_TRANSLATIONS.coreFeatures.name,
			title:
				MODULE_TRANSLATIONS.coreFeatures.title[currentLanguage] ||
				MODULE_TRANSLATIONS.coreFeatures.title.en,
			icon: "star",
			description: MODULE_TRANSLATIONS.coreFeatures.description,
		},
		{
			tabId: "tipsAndTricks",
			name: MODULE_TRANSLATIONS.tipsAndTricks.name,
			title:
				MODULE_TRANSLATIONS.tipsAndTricks.title[currentLanguage] ||
				MODULE_TRANSLATIONS.tipsAndTricks.title.en,
			icon: "lightbulb",
			description: MODULE_TRANSLATIONS.tipsAndTricks.description,
		},
		{
			tabId: "screenshots",
			name: MODULE_TRANSLATIONS.screenshots.name,
			title:
				MODULE_TRANSLATIONS.screenshots.title[currentLanguage] ||
				MODULE_TRANSLATIONS.screenshots.title.en,
			icon: "image",
			description: MODULE_TRANSLATIONS.screenshots.description,
		},
		{
			tabId: "whyPlayHere",
			name: MODULE_TRANSLATIONS.whyPlayHere.name,
			title:
				MODULE_TRANSLATIONS.whyPlayHere.title[currentLanguage] ||
				MODULE_TRANSLATIONS.whyPlayHere.title.en,
			icon: "thumbs-up",
			description: MODULE_TRANSLATIONS.whyPlayHere.description,
		},
		{
			tabId: "platformInfo",
			name: MODULE_TRANSLATIONS.platformInfo.name,
			title:
				MODULE_TRANSLATIONS.platformInfo.title[currentLanguage] ||
				MODULE_TRANSLATIONS.platformInfo.title.en,
			icon: "monitor",
			description: MODULE_TRANSLATIONS.platformInfo.description,
		},
		{
			tabId: "faq",
			name: MODULE_TRANSLATIONS.faq.name,
			title:
				MODULE_TRANSLATIONS.faq.title[currentLanguage] ||
				MODULE_TRANSLATIONS.faq.title.en,
			icon: "help-circle",
			description: MODULE_TRANSLATIONS.faq.description,
		},
		{
			tabId: "popularVideos",
			name: MODULE_TRANSLATIONS.popularVideos.name,
			title:
				MODULE_TRANSLATIONS.popularVideos.title[currentLanguage] ||
				MODULE_TRANSLATIONS.popularVideos.title.en,
			icon: "video",
			description: MODULE_TRANSLATIONS.popularVideos.description,
		},
	]
}

"use client"

import { useState, useEffect, useCallback } from "react"
import { fetchGet, fetchPost, fetchPut } from "@repo/utils/react"
import { toast } from "sonner"
import useSWR from "swr"

type RelatedGame = {
	// 游戏ID
	id: string
	// 游戏名称
	name: string
	// 游戏访问路径
	slug?: string
	// 游戏图片
	image?: string
	// 关联类型
	relationType?: "similar" | "series" | "recommended"
	// 排序
	order?: number
	// 是否自动关联
	isAuto?: boolean
}

type RelatedGameForm = {
	// 关联配置
	relatedGamesConfig: {
		// 关联类型
		type: "similar" | "series" | "recommended"
		// 关联模式
		mode: "auto" | "manual"
		// 关联规则
		rules: {
			// 是否关联相同分类
			sameCategory: boolean
			// 是否关联相同标签
			sameTags: boolean
			// 自动关联最大数量
			maxAutoItems: number
		}
	}
	// 关联游戏
	relatedGames: RelatedGame[]
}

interface UseGameRelatedGamesFormProps {
	projectId: string
	gameId: string
}

export function useGameRelatedGamesForm({
	projectId,
	gameId,
}: UseGameRelatedGamesFormProps) {
	const [isSaving, setIsSaving] = useState(false)

	// 获取游戏数据
	const {
		data,
		error,
		isLoading,
		mutate: refreshData,
	} = useSWR<any>(`/api/project-games?id=${gameId}`, fetchGet, {
		revalidateOnFocus: false,
		revalidateOnReconnect: false,
		onError: (err) => {
			console.error("获取游戏数据失败:", err)
			toast.error("获取游戏数据失败")
		},
	})

	// 表单状态
	const [formData, setFormData] = useState<RelatedGameForm>({
		relatedGamesConfig: {
			type: "similar",
			mode: "auto",
			rules: {
				sameCategory: true,
				sameTags: true,
				maxAutoItems: 3,
			},
		},
		relatedGames: [],
	})

	// 当数据加载完成后更新表单
	useEffect(() => {
		if (data) {
			// 直接从ProjectGame中获取相关游戏数据
			const relatedGamesConfig = data.relatedGamesConfig || {}
			const relatedGames = data.relatedGames || []

			// 将数据转换为表单格式
			setFormData({
				relatedGamesConfig: {
					type: relatedGamesConfig.type || "similar",
					mode: relatedGamesConfig.mode || "auto",
					rules: relatedGamesConfig.rules || {
						sameCategory: true,
						sameTags: true,
						maxAutoItems: 3,
					},
				},
				relatedGames: Array.isArray(relatedGames)
					? relatedGames.map((id) => ({ id, name: id }))
					: [],
			})
		}
	}, [data])

	// 更新表单数据
	const updateFormData = useCallback((newData: Partial<RelatedGameForm>) => {
		setFormData((prev) => ({
			...prev,
			...newData,
		}))
	}, [])

	// 更新相关游戏数据
	const updateRelatedGamesData = useCallback(async () => {
		if (!data) return

		setIsSaving(true)

		try {
			// 准备要保存的数据
			const relatedGamesConfig = {
				type: formData.relatedGamesConfig.type,
				mode: formData.relatedGamesConfig.mode,
				rules: formData.relatedGamesConfig.rules,
				enabled: true,
				maxDisplay: 6,
				showReason: true,
			}

			// 提取游戏ID列表
			const relatedGames = formData.relatedGames.map((game) => game.id)

			// 直接更新ProjectGame - 修改为匹配路由的update-game路径
			await fetchPut(`/api/project-games/update-game`, {
				gameId,
				relatedGamesConfig,
				relatedGames,
			})

			toast.success("相关游戏数据已保存")
			refreshData()
		} catch (error) {
			console.error("保存相关游戏数据失败:", error)
			toast.error("保存相关游戏数据失败")
		} finally {
			setIsSaving(false)
		}
	}, [data, formData, projectId, gameId, refreshData])

	return {
		formData,
		updateFormData,
		updateRelatedGamesData,
		isLoading,
		isSaving,
		error,
	}
}

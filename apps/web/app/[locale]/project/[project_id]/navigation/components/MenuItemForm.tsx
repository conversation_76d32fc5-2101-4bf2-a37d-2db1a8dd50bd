"use client"

import { useEffect } from "react"
import { IconSelector } from "@/lib/components/icons/IconSelector"
import { useFormWithTranslation, TranslationConfirmDialog } from "@/lib/components/translation"
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@repo/ui/components"
import { NavItem } from "../types"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Button } from "@repo/ui/components"
import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { toast } from "sonner"

// 定义表单验证模式
const formSchema = z.object({
	label: z.string().min(1, { message: "菜单名称不能为空" }),
	iconName: z.string().optional(),
	href: z.string().min(1, { message: "链接地址不能为空" }),
	target: z.enum(["_self", "_blank"]).default("_self"),
})

interface MenuItemFormProps {
	item: NavItem
	onSave: (item: NavItem, options?: { isTranslation?: boolean }) => void
	isSaving?: boolean
	projectId: string
}

export function MenuItemForm({
	item,
	onSave,
	isSaving = false,
	projectId,
}: MenuItemFormProps) {
	const { currentLanguage, defaultLanguage } = useTranslationContext()

	// 判断当前是否为默认语言
	const isDefaultLanguage = currentLanguage === defaultLanguage
	// 使用 react-hook-form 和 zod 进行表单验证
	const form = useForm({
		resolver: zodResolver(formSchema),
		defaultValues: {
			label: item.label,
			iconName: item.iconName || "",
			href: item.href,
			target: item.target || "_self" as const,
		},
	})

	// 当传入的item变化时更新表单值
	useEffect(() => {
		form.reset({
			label: item.label,
			iconName: item.iconName || "",
			href: item.href,
			target: item.target || "_self" as const,
		})
	}, [item, currentLanguage, form])

	// 原始的提交函数
	const originalSubmit = async (values: any, options?: { isTranslation?: boolean }) => {
		// 构建更新后的菜单项对象
		const updatedItem: NavItem = isDefaultLanguage
			? {
					...item,
					label: values.label,
					iconName: values.iconName,
					href: values.href,
					target: values.target as "_self" | "_blank",
				}
			: {
					...item,
					label: values.label, // 非默认语言下只更新label字段
				};

		return onSave(updatedItem, options)
	}

	// 集成翻译确认功能
	const { handleSubmit: enhancedSubmit, translationDialogProps } = useFormWithTranslation(
		originalSubmit,
		{
			projectId,
			contentType: "navigation-menu",
			confirmOptions: {
				title: "发起导航菜单翻译任务",
				description: "检测到您保存了导航菜单，是否同时发起翻译任务？翻译任务将会把当前菜单翻译到其他语言版本。",
				confirmText: "发起翻译",
				cancelText: "仅保存",
			}
		}
	)

	// 处理表单提交
	async function onSubmit(values: any) {
		try {
			await enhancedSubmit(values)
			toast.success("菜单项保存成功")
		} catch (error) {
			console.error("保存菜单项失败:", error)
			toast.error("保存菜单项失败")
		}
	}

	// 移除自动保存功能，只在用户点击保存设置按钮时触发保存

	return (
		<Form {...form}>
			<form
				className="space-y-6"
				onSubmit={form.handleSubmit(onSubmit)}
				onReset={() => form.reset()}
			>
				<div className="grid md:grid-cols-2 gap-6">
					<FormField
						control={form.control}
						name="label"
						render={({ field }) => (
							<FormItem>
								<FormLabel>菜单名称</FormLabel>
								<FormControl>
									<Input placeholder="输入菜单名称" {...field} />
								</FormControl>
								<FormDescription>
									菜单名称建议不超过10个字符，以保证显示效果
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="iconName"
						render={({ field }) => (
							<FormItem>
								<FormLabel>菜单图标</FormLabel>
								<FormControl>
									{isDefaultLanguage ? (
										<IconSelector
											value={field.value || ""}
											onChange={field.onChange}
										/>
									) : (
										<div className="flex items-center h-10 px-3 py-2 text-sm border rounded-md bg-muted">
											{field.value ? (
												<div className="flex items-center gap-2">
													<span className="truncate">{field.value}</span>
												</div>
											) : (
												<span className="text-muted-foreground">无图标</span>
											)}
										</div>
									)}
								</FormControl>
								<FormDescription>
									{!isDefaultLanguage
										? "非默认语言下不可编辑图标"
										: "选择一个图标或上传自定义图标"}
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid md:grid-cols-2 gap-6">
					<FormField
						control={form.control}
						name="href"
						render={({ field }) => (
							<FormItem>
								<FormLabel>链接地址</FormLabel>
								<FormControl>
									<Input
										placeholder="输入链接地址"
										{...field}
										disabled={!isDefaultLanguage}
										className={!isDefaultLanguage ? "bg-muted" : ""}
									/>
								</FormControl>
								<FormDescription>
									{!isDefaultLanguage
										? "非默认语言下不可编辑链接地址"
										: "内部链接格式如: /games，外部链接需包含http://或https://"}
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="target"
						render={({ field }) => (
							<FormItem>
								<FormLabel>打开方式</FormLabel>
								{isDefaultLanguage ? (
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value || "_self"}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="选择打开方式" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="_self">当前窗口</SelectItem>
											<SelectItem value="_blank">新窗口</SelectItem>
										</SelectContent>
									</Select>
								) : (
									<div className="flex items-center h-10 px-3 py-2 text-sm border rounded-md bg-muted">
										{field.value === "_self" ? "当前窗口" : "新窗口"}
									</div>
								)}
								<FormDescription>
									{!isDefaultLanguage
										? "非默认语言下不可编辑打开方式"
										: "选择链接的打开方式"}
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* 底部固定按钮 */}
				<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
					<div className="max-w-6xl mx-auto flex justify-end w-full">
						<Button
							type="reset"
							variant="outline"
							className="mr-3"
							disabled={isSaving}
						>
							重置
						</Button>
						<Button type="submit" disabled={isSaving}>
							{isSaving ? "保存中..." : "保存设置"}
						</Button>
					</div>
				</div>
			</form>

			{/* 翻译确认对话框 */}
			<TranslationConfirmDialog {...translationDialogProps} />
		</Form>
	)
}

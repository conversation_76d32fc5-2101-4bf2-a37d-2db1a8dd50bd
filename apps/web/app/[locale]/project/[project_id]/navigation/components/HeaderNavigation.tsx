"use client"

import TranslationSwitcher from "@/lib/components/translation/TranslationSwitcher"
import { ExternalLink, Link as LinkIcon } from "lucide-react"
import {
	NavigationType,
	useNavigationSettings,
} from "../hooks/useNavigationSettings"
import { MenuItemForm } from "./MenuItemForm"
import { MenuList } from "./MenuList"

export default function HeaderNavigation({ projectId }: { projectId: string }) {
	// 使用自定义 hook 获取导航数据
	const {
		isLoading,
		menuItems,
		selectedMenuItem,
		setSelectedMenuItem,
		addMenuItem,
		addSubMenuItem,
		updateMenuItem,
		deleteMenuItem,
		reorderMenuItems,
	} = useNavigationSettings(projectId, NavigationType.HEADER)

	return (
		<div className="grid md:grid-cols-5 gap-5">
			{/* 左侧菜单列表 */}
			<div className="md:col-span-2">
				<div className="bg-card rounded-lg shadow-sm p-4 mb-5 border min-h-[480px]">
					<MenuList
						items={menuItems}
						selectedItemId={selectedMenuItem?.id}
						onSelectItem={setSelectedMenuItem}
						onDeleteItem={deleteMenuItem}
						onAddItem={addMenuItem}
						onAddSubItem={addSubMenuItem}
						onReorderItems={reorderMenuItems}
						isLoading={isLoading}
						title="头部导航菜单"
					/>
				</div>
			</div>

			{/* 右侧编辑区域 */}
			<div className="md:col-span-3">
				{selectedMenuItem ? (
					<div className="bg-card rounded-lg shadow-sm p-4 mb-5 border">
						<div className="flex justify-between items-start mb-4">
							<h3 className="text-base font-medium text-foreground">
								{selectedMenuItem.id.startsWith("menu_")
									? "添加菜单项"
									: "编辑菜单项"}
							</h3>
							<TranslationSwitcher />
						</div>
						<div className="text-sm text-muted-foreground mb-4">
							{selectedMenuItem.target === "_blank" ? (
								<div className="flex items-center gap-1">
									<ExternalLink className="h-3.5 w-3.5" /> 外部链接
								</div>
							) : (
								<div className="flex items-center gap-1">
									<LinkIcon className="h-3.5 w-3.5" /> 内部链接
								</div>
							)}
						</div>
						<MenuItemForm
							key={`${selectedMenuItem.id}-${selectedMenuItem.label}`}
							item={selectedMenuItem}
							onSave={updateMenuItem}
							projectId={projectId}
						/>
					</div>
				) : (
					<div className="bg-card rounded-lg shadow-sm p-4 mb-5 min-h-[480px] border">
						<p className="text-muted-foreground text-center py-6 text-sm">
							请选择一个菜单项进行编辑或添加新菜单
						</p>
					</div>
				)}
			</div>
		</div>
	)
}

#!/usr/bin/env node

/**
 * MQ重连测试脚本
 * 用于测试修复后的AMQPClient在重连时是否还会出现"No channel available"错误
 */

const { AMQPClient } = require('./packages/utils/dist/amqp/AMQPClient.js');
const { AMQPConsumer } = require('./packages/utils/dist/amqp/AMQPConsumer.js');

// 模拟MQ服务器URL（请根据实际情况修改）
const MQ_URL = process.env.MQ_URL || 'amqp://localhost:5672';

async function testReconnect() {
    console.log('开始MQ重连测试...');
    
    // 创建客户端
    const client = new AMQPClient(MQ_URL, {}, {
        initialInterval: 1000,
        maxInterval: 5000,
        maxRetries: 10,
        enableWebhook: false
    });
    
    // 创建多个消费者来模拟并发场景
    const consumers = [];
    const queueNames = ['test-queue-1', 'test-queue-2', 'test-queue-3'];
    
    try {
        // 初始连接
        await client.connect();
        console.log('初始连接成功');
        
        // 创建消费者
        for (const queueName of queueNames) {
            const consumer = new AMQPConsumer(
                client,
                queueName,
                async (msg) => {
                    if (msg) {
                        console.log(`收到消息: ${queueName} - ${msg.content.toString()}`);
                        await consumer.ackMessage(msg);
                    }
                }
            );
            
            await consumer.start();
            consumers.push(consumer);
            console.log(`消费者 ${queueName} 启动成功`);
        }
        
        // 模拟连接断开（这里需要手动断开MQ服务器或网络）
        console.log('请手动断开MQ服务器连接来测试重连功能...');
        console.log('观察日志中是否还会出现"No channel available"错误');
        
        // 保持运行30秒
        await new Promise(resolve => setTimeout(resolve, 30000));
        
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    } finally {
        // 清理资源
        console.log('清理资源...');
        for (const consumer of consumers) {
            try {
                await consumer.stop();
            } catch (error) {
                console.log('停止消费者时的错误（这是正常的）:', error.message);
            }
        }
        
        try {
            await client.close();
        } catch (error) {
            console.log('关闭客户端时的错误（这是正常的）:', error.message);
        }
        
        console.log('测试完成');
    }
}

// 处理未捕获的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
    console.log('捕获到未处理的Promise拒绝:', reason);
});

// 处理进程退出
process.on('SIGINT', () => {
    console.log('收到退出信号，正在清理...');
    process.exit(0);
});

// 运行测试
testReconnect().catch(console.error);

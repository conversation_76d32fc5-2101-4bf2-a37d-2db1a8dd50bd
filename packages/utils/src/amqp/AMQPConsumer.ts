import { getLogger } from "@repo/logger"
import type amqp from "amqplib"
import type { AMQPClient } from "./AMQPClient"

const logger = getLogger("AMQPConsumer")

export type MessageHandler = (
	msg: amqp.ConsumeMessage | null,
) => Promise<void> | void

export interface ConsumerOptions {
	prefetchCount?: number
	noAck?: boolean
	exclusive?: boolean
	consumerTag?: string
	mode?: ConsumeMode
}

export enum ConsumeMode {
	PUSH = "push",
	PULL = "pull",
}

export class AMQPConsumer {
	private client: AMQPClient
	private queueName: string
	private handler: MessageHandler
	private options: ConsumerOptions
	private mode: ConsumeMode
	private consumerTag?: string
	private isRunning = false
	private pullInterval?: NodeJS.Timeout

	constructor(
		client: AMQPClient,
		queueName: string,
		handler: MessageHandler,
		options: ConsumerOptions = {},
	) {
		this.client = client
		this.queueName = queueName
		this.handler = handler
		this.mode = options.mode || ConsumeMode.PUSH
		this.options = {
			prefetchCount: 1,
			noAck: false,
			exclusive: false,
			...options,
		}
	}

	async ackMessage(message: amqp.ConsumeMessage): Promise<void> {
		await this.client.ackMessage(message)
	}

	async nackMessage(
		message: amqp.ConsumeMessage,
		requeue = true,
	): Promise<void> {
		await this.client.nackMessage(message, requeue)
	}

	async start(prefetch = 1): Promise<void> {
		if (this.isRunning) {
			throw new Error("Consumer is already running")
		}

		try {
			const channel = await this.client.getChannel()
			if (!channel) {
				throw new Error("No channel available - MQ client may be reconnecting")
			}

			// 设置预取数量
			await channel.prefetch(prefetch)
			await this.client.assertQueue(this.queueName)

			this.isRunning = true

			if (this.mode === ConsumeMode.PUSH) {
				this.consumerTag = (
					await channel.consume(this.queueName, this.handler, this.options)
				).consumerTag
			} else {
				await this.startPullMode(channel)
			}

			logger.info(
				`Started consuming from queue: ${this.queueName}, prefetch: ${prefetch}`,
			)
		} catch (error) {
			this.isRunning = false
			logger.error(`Failed to start consumer for queue ${this.queueName}:`, error)
			throw error
		}
	}

	private async startPullMode(channel: amqp.Channel): Promise<void> {
		const pullMessage = async () => {
			if (!this.isRunning) return

			try {
				const msg = await channel.get(this.queueName, {
					noAck: this.options.noAck,
				})

				if (msg) {
					await this.handler(msg as any)
				}
			} catch (error) {
				logger.error("Error pulling message:", error)
			}
		}

		// 设置轮询间隔
		this.pullInterval = setInterval(pullMessage, 1000)
		await pullMessage() // 立即执行第一次拉取
	}

	async stop(): Promise<void> {
		if (!this.isRunning) {
			return
		}

		this.isRunning = false

		try {
			const channel = await this.client.getChannel()
			if (!channel) {
				logger.warn("Channel not available during stop, cleaning up local state only")
				// 如果没有channel，只清理本地状态
				if (this.mode === ConsumeMode.PUSH) {
					this.consumerTag = undefined
				} else if (this.mode === ConsumeMode.PULL && this.pullInterval) {
					clearInterval(this.pullInterval)
					this.pullInterval = undefined
				}
				return
			}

			if (this.mode === ConsumeMode.PUSH && this.consumerTag) {
				await channel.cancel(this.consumerTag)
				this.consumerTag = undefined
			} else if (this.mode === ConsumeMode.PULL && this.pullInterval) {
				clearInterval(this.pullInterval)
				this.pullInterval = undefined
			}
		} catch (error) {
			logger.warn("Error during consumer stop, cleaning up local state:", error)
			// 发生错误时，确保清理本地状态
			if (this.mode === ConsumeMode.PUSH) {
				this.consumerTag = undefined
			} else if (this.mode === ConsumeMode.PULL && this.pullInterval) {
				clearInterval(this.pullInterval)
				this.pullInterval = undefined
			}
		}
	}

	async pause(): Promise<void> {
		if (!this.isRunning) {
			return
		}

		await this.stop()
	}

	/**
	 * 关闭 MQ 连接
	 */
	async close() {
		try {
			const channel = await this.client.getChannel()
			if (channel) {
				await channel.close()
			}
		} catch (error) {
			logger.warn("Error during consumer close:", error)
			// 忽略关闭时的错误，因为连接可能已经断开
		}
	}

	async resume(): Promise<void> {
		if (this.isRunning) {
			return
		}

		await this.start()
	}

	isActive(): boolean {
		return this.isRunning
	}
}

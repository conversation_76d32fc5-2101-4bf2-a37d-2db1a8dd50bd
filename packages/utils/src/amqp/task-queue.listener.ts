import { getLogger } from "@repo/logger"
import {
	type RpaMessage,
	RpaTask,
	TASK_STATUS_FAILED,
	TASK_STATUS_PROCESSING,
	TASK_STATUS_SUCCESS,
} from "@repo/shared-types"
import type { AMQPClient } from "./AMQPClient"
import { AMQPConsumer } from "./AMQPConsumer"
import type { ConsumeMessage } from "amqplib"
import { StatusUpdateOperations } from "./status-update.operations"
import { Colors, WebHook } from "../server"
const logger = getLogger(import.meta.url)

export class TaskQueueListener {
	protected readonly client: AMQPClient
	private readonly consumeQueue: string
	/**
	 * 响应队列名称，通常是用于接收任务结果和过程的队列名称
	 */
	private readonly responseQueue: string
	protected consumer?: AMQPConsumer
	private messageCounter: { timestamp: number; count: number }[] = []
	private readonly windowSize: number = 60 * 1000 // 1分钟的窗口期（毫秒）
	private rateLimit = 5 // 默认限流阈值为5条/分钟
	private activeTasks: Map<string, AbortController> = new Map()
	protected noAck = true
	private taskTimeout: number = 3 * 60 * 1000 // 默认任务超时时间为3分钟
	constructor(
		client: AMQPClient,
		consumeQueue: string,
		responseQueue: string,
		taskTimeout: number = 3 * 60 * 1000,
	) {
		this.client = client
		this.consumeQueue = consumeQueue
		this.responseQueue = responseQueue
		this.taskTimeout = taskTimeout
		// 注册进程退出处理
		process.once("SIGTERM", () => this.destroy())
		process.once("SIGINT", () => this.destroy())
	}

	// 覆盖父类的 init 方法，添加对 dataQueue 的初始化
	async _init(): Promise<void> {
		// 确保队列存在
		await this.client.assertQueue(this.consumeQueue)
		if (this.responseQueue) {
			await this.client.assertQueue(this.responseQueue)
		}
	}

	async _start(prefetch = 1): Promise<void> {
		try {
			logger.info(
				`Starting task queue listener for queue: ${this.consumeQueue}`,
			)
			// 确保连接已建立
			if (!this.client.isConnected()) {
				await this.client.connect()
			}

			// 初始化队列
			await this._init()
			// 启动消费者
			await this.consumer?.start(prefetch)
			logger.info(
				`task queue listener ${this.consumeQueue} started successfully`,
			)
		} catch (error) {
			logger.error("Failed to start task queue listener:", error)
			throw error
		}
	}

	// 启动消费，不自动确认消息，等待上一个消息消费完成才启动下一个
	async startConsuming(prefetch = 1, noAck = false): Promise<void> {
		// 初始化消费者
		this.consumer = new AMQPConsumer(
			this.client,
			this.consumeQueue,
			(message) => this.handleMessage(message),
			{
				noAck: noAck,
				prefetchCount: prefetch,
			},
		)
		this.noAck = noAck
		await this._start(prefetch)
	}

	// 启动带限流的消费，不自动确认消息，等待上一个消息消费完成才启动下一个
	async startRateLimitedConsuming(
		prefetch = 1,
		rateLimit = 2,
		noAck = false,
	): Promise<void> {
		this.rateLimit = rateLimit
		this.noAck = noAck
		// 如果启用了限流，替换消费者的处理函数
		if (this.rateLimit > 0) {
			// 创建带限流的消费者
			this.consumer = new AMQPConsumer(
				this.client,
				this.consumeQueue,
				(message) => this.handleMessageWithRateLimit(message),
				{
					noAck: this.noAck,
					prefetchCount: prefetch,
				},
			)
		}
		await this._start(prefetch)
	}

	private async handleMessageWithRateLimit(
		message: ConsumeMessage | null,
	): Promise<void> {
		if (!message) return

		const now = Date.now()
		// 清理过期的记录
		this.messageCounter = this.messageCounter.filter(
			(record) => now - record.timestamp < this.windowSize,
		)

		// 计算当前窗口内的总消息数
		const totalMessages = this.messageCounter.reduce(
			(sum, record) => sum + record.count,
			0,
		)

		if (totalMessages >= this.rateLimit) {
			// 如果超过限流阈值，重新入队消息
			await this.consumer?.nackMessage(message, true)
			logger.warn(
				`${this.consumeQueue}达到速率限制 (${totalMessages}/${this.rateLimit})，消息重新入队`,
			)
			return
		}

		// 记录新消息
		this.messageCounter.push({ timestamp: now, count: 1 })

		// 处理消息
		await this.handleMessage(message)
	}

	protected async handleMessage(message: ConsumeMessage | null): Promise<void> {
		if (!message) return
		let hasAck = false
		try {
			// 解析消息内容
			const msg = JSON.parse(message.content.toString()) as RpaMessage
			logger.info(`收到MQ队列:${this.consumeQueue}任务消息: ${message.content.toString()}`)

			// 创建状态更新操作对象（每条消息都需要一个独立的对象，保证messageId唯一，便于runtimeLogging输出）
			const statusOps = new StatusUpdateOperations(
				this.client,
				this.responseQueue,
				msg,
			)

			// 创建一个唯一的任务ID
			const taskId = `${this.consumeQueue}_${Date.now()}`

			// 创建 AbortController 用于任务取消
			const abortController = new AbortController()
			this.activeTasks.set(taskId, abortController)

			// 记录任务开始时间
			const startTime = Date.now()

			// 设置任务超时
			const timeoutPromise = new Promise((_, reject) => {
				setTimeout(() => {
					reject(
						new Error(`Task ${taskId} timed out after ${this.taskTimeout}ms`),
					)
				}, this.taskTimeout)
			})

			// 设置3分钟确认检查
			const threeMinutes = 3 * 60 * 1000 // 3分钟（毫秒）
			const confirmationCheckerId = setInterval(async () => {
				const currentTime = Date.now()
				const elapsedTime = currentTime - startTime

				if (elapsedTime >= threeMinutes) {
					// 如果任务执行时间已超过3分钟，且消息尚未被确认,立即确认消息，避免MQ连接心跳超时
					if (!this.noAck && this.consumer && !message.fields.deliveryTag) {
						try {
							await this.consumer.ackMessage(message)
							logger.info(`任务 ${taskId} 执行时间已超过3分钟，已确认消息`)
							hasAck = true
							// 更新任务状态
							await statusOps.update(TASK_STATUS_PROCESSING, {
								error_msg: "任务执行中，已超过3分钟",
							})
						} catch (ackError) {
							logger.error(`确认消息失败 [${taskId}]:`, ackError)
						}
					}
					// 清除定时器
					clearInterval(confirmationCheckerId)
				}
			}, 10000) // 每10秒检查一次

			// 执行任务
			const taskPromise = this.processTask(msg, statusOps, taskId)

			try {
				// 使用 Promise.race 来处理超时
				await Promise.race([taskPromise, timeoutPromise])
			} catch (error: any) {
				logger.error(`任务执行失败[${taskId}]:`, error)
			} finally {
				// 清除定时器
				clearInterval(confirmationCheckerId)
				this.activeTasks.delete(taskId)
			}
		} catch (error: any) {
			logger.error("消息处理失败:", error)
		} finally {
			if (!hasAck && !this.noAck && this.consumer) {
				await this.consumer.ackMessage(message)
			}
		}
	}

	private async processTask(
		msg: RpaMessage,
		statusOps: StatusUpdateOperations,
		taskId: string,
	): Promise<any> {
		try {
			logger.info(`开始执行任务 ${taskId}`)
			const result = await this.execute(msg, statusOps)
			await statusOps.update(TASK_STATUS_SUCCESS, result)
			logger.info(`任务 ${taskId} 执行成功`)
			return result
		} catch (error: any) {
			logger.error(`任务 ${taskId} 执行失败:`, error)
			await statusOps.update(TASK_STATUS_FAILED, {
				error_msg: error.message,
			})
			throw error
		}
	}

	// 子类可以继承重写这个方法
	async execute(
		message: RpaMessage,
		statusOps?: StatusUpdateOperations,
	): Promise<any> {
		// 默认实现
		logger.info(`执行任务:${this.consumeQueue},${message.id}，当前类未实现execute方法`)
		return null
	}

	// 覆盖父类的 destroy 方法，添加任务取消功能
	async destroy(): Promise<void> {
		// 取消所有正在执行的任务
		for (const [taskId, controller] of this.activeTasks) {
			controller.abort()
			logger.info(`取消任务: ${taskId}`)
		}
		this.activeTasks.clear()

		logger.info("Shutting down BaseListener...")
		try {
			await this.consumer?.stop()
		} catch (error) {
			logger.warn("Error stopping consumer during destroy:", error)
			// 继续执行，不让错误阻止清理过程
		}
		logger.info("BaseListener shutdown complete")
	}

	protected async sendWebHook(taskName: string, taskID: string, errorMessage: string) {
		// Send webhook notification for the error
		await new WebHook()
			.title("🚨 外链提交任务执行失败", Colors.Error)
			.kv("任务类型", taskName)
			.kv("任务ID", taskID)
			.kv("任务状态", "失败")
			.kv("错误信息", errorMessage, Colors.Error)
			.timestamp()
			.send()
	}
}

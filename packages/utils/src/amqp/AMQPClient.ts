import type { AMQPMessageQueue } from "@repo/shared-types"
import amqp from "amqplib"
import { WebHook, Colors } from "../server"
import { getLogger } from "@repo/logger"

const logger = getLogger("AMQPClient")

export interface ChannelOptions {
	confirmMode?: boolean
}

export interface ReconnectOptions {
	/** 初始重连间隔（毫秒） */
	initialInterval?: number
	/** 最大重连间隔（毫秒） */
	maxInterval?: number
	/** 重连间隔增长因子 */
	multiplier?: number
	/** 最大重试次数，0表示无限重试 */
	maxRetries?: number
	/** 是否启用Webhook通知 */
	enableWebhook?: boolean
	/** Webhook通知URL，不设置则使用环境变量 */
	webhookUrl?: string
	/** 连续失败多少次后发送Webhook通知 */
	webhookThreshold?: number
}

/**
 * 连接状态枚举
 */
enum ConnectionState {
	DISCONNECTED = "disconnected",
	CONNECTING = "connecting",
	CONNECTED = "connected",
	RECONNECTING = "reconnecting",
}

export class AMQPClient {
	protected connection: amqp.ChannelModel | null = null
	protected channel: amqp.Channel | amqp.ConfirmChannel | null = null
	private readonly url: string
	protected queues: Set<string> = new Set()
	private channelOptions: ChannelOptions
	private heartbeatInterval = 30000
	private heartbeatTimer: NodeJS.Timeout | null = null

	// 重连相关属性
	private reconnectTimer: NodeJS.Timeout | null = null
	private currentRetryCount = 0
	private connectionState: ConnectionState = ConnectionState.DISCONNECTED
	private connectingPromise: Promise<void> | null = null

	// 重连配置
	private initialReconnectInterval: number
	private maxReconnectInterval: number
	private reconnectMultiplier: number
	private maxRetries: number
	private enableWebhook: boolean
	private webhookUrl?: string
	private webhookThreshold: number
	private webhookSent = false

	constructor(
		url: string,
		options: ChannelOptions = {},
		reconnectOptions: ReconnectOptions = {}
	) {
		this.url = url
		this.channelOptions = options

		// 初始化重连配置
		this.initialReconnectInterval = reconnectOptions.initialInterval || 1000
		this.maxReconnectInterval = reconnectOptions.maxInterval || 30000
		this.reconnectMultiplier = reconnectOptions.multiplier || 1.5
		this.maxRetries = reconnectOptions.maxRetries !== undefined ? reconnectOptions.maxRetries : 100
		this.enableWebhook = reconnectOptions.enableWebhook !== undefined ? reconnectOptions.enableWebhook : true
		this.webhookUrl = reconnectOptions.webhookUrl
		this.webhookThreshold = reconnectOptions.webhookThreshold || 3
	}

	/**
	 * 连接到AMQP服务器
	 */
	async connect() {
		// 如果已经在连接中，等待当前连接完成
		if (this.connectingPromise) {
			logger.warn("已有连接操作正在进行中，等待完成...")
			await this.connectingPromise
			return
		}

		// 如果已经在连接中，直接返回
		if (this.connectionState === ConnectionState.CONNECTING ||
			this.connectionState === ConnectionState.RECONNECTING) {
			logger.warn("已有连接操作正在进行中，忽略此次连接请求")
			return
		}

		// 创建连接Promise并保存
		this.connectingPromise = this.doConnect()

		try {
			await this.connectingPromise
		} finally {
			this.connectingPromise = null
		}
	}

	/**
	 * 执行实际的连接操作
	 */
	private async doConnect() {
		// 更新状态为连接中
		this.connectionState = ConnectionState.CONNECTING

		try {
			// 清理之前的连接
			await this.cleanup(false)

			logger.info(`正在连接到MQ服务器: ${this.url}`)
			this.connection = (await amqp.connect(this.url, {
				heartbeat: 120,
			})) as amqp.ChannelModel

			// 监听连接错误
			this.connection?.on("error", (err) => {
				logger.error("AMQP连接错误:", err)
				this.handleConnectionError(err)
			})

			// 监听连接关闭
			this.connection?.on("close", () => {
				logger.warn("AMQP连接已关闭，准备重新连接...")
				this.handleConnectionError(new Error("连接已关闭"))
			})

			// 根据选项创建对应类型的通道
			if (this.connection) {
				if (this.channelOptions.confirmMode) {
					this.channel = await this.connection.createConfirmChannel()
				} else {
					this.channel = await this.connection.createChannel()
				}
			}

			if (this.channel) {
				this.channel.on("close", () => {
					logger.warn("MQ通道已关闭，准备重新连接...")
					this.handleChannelError(new Error("通道已关闭"))
				})

				this.channel.on("error", (err) => {
					logger.error("MQ通道错误:", err)
					this.handleChannelError(err)
				})
			}

			// 添加心跳检测
			this.startHeartbeatCheck()

			// 连接成功，重置重试计数和状态
			this.currentRetryCount = 0
			this.connectionState = ConnectionState.CONNECTED
			this.webhookSent = false

			logger.info(`MQ连接成功 => ${this.url}`)
		} catch (error) {
			logger.error("MQ连接失败:", error)
			// 连接失败，触发重连逻辑
			this.handleConnectionError(error)
		}
	}

	/**
	 * 启动心跳检测
	 */
	private startHeartbeatCheck() {
		this.stopHeartbeatCheck()
		this.heartbeatTimer = setInterval(() => {
			if (!this.isConnected()) {
				logger.warn("心跳检测：检测到连接断开，准备重新连接...")
				this.scheduleReconnect()
			}
		}, this.heartbeatInterval)
	}

	/**
	 * 停止心跳检测
	 */
	private stopHeartbeatCheck() {
		if (this.heartbeatTimer) {
			clearInterval(this.heartbeatTimer)
			this.heartbeatTimer = null
		}
	}

	/**
	 * 处理连接错误
	 */
	private handleConnectionError(error: any) {
		// 如果已经在重连中，不重复处理
		if (this.connectionState === ConnectionState.RECONNECTING) {
			return
		}

		logger.warn(`MQ连接出错，准备重新连接: ${error.message || error}`)
		this.scheduleReconnect()
	}

	/**
	 * 处理通道错误
	 */
	private handleChannelError(error: any) {
		// 如果已经在重连中，不重复处理
		if (this.connectionState === ConnectionState.RECONNECTING) {
			return
		}

		logger.warn(`MQ通道出错，准备重新连接: ${error.message || error}`)
		this.scheduleReconnect()
	}

	/**
	 * 安排重连
	 */
	private scheduleReconnect() {
		// 清除之前的重连定时器
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer)
			this.reconnectTimer = null
		}

		// 更新状态为重连中
		this.connectionState = ConnectionState.RECONNECTING

		// 检查是否达到最大重试次数
		if (this.maxRetries > 0 && this.currentRetryCount >= this.maxRetries) {
			logger.error(`MQ连接失败: 达到最大重试次数 ${this.maxRetries}，停止重连`)
			this.sendWebhookNotification(
				"MQ连接失败",
				`达到最大重试次数 ${this.maxRetries}，停止重连`,
				true
			)
			this.connectionState = ConnectionState.DISCONNECTED
			return
		}

		// 计算下一次重连间隔（指数退避）
		const reconnectInterval = this.calculateNextReconnectInterval()
		this.currentRetryCount++

		logger.info(`安排第 ${this.currentRetryCount} 次重连，将在 ${reconnectInterval}ms 后尝试...`)

		// 检查是否需要发送Webhook通知
		if (this.enableWebhook &&
			this.currentRetryCount >= this.webhookThreshold &&
			!this.webhookSent) {
			this.sendWebhookNotification(
				"RPA连接MQ异常",
				`已尝试重连 ${this.currentRetryCount} 次，将继续尝试重连`,
				false
			)
			this.webhookSent = true
		}

		// 安排下一次重连
		this.reconnectTimer = setTimeout(() => {
			this.executeReconnect()
		}, reconnectInterval)
	}

	/**
	 * 执行重连
	 */
	private async executeReconnect() {
		try {
			logger.info(`执行第 ${this.currentRetryCount} 次重连...`)
			await this.cleanup(false)
			await this.doConnect()

			// 如果连接成功，重新声明之前的队列
			if (this.isConnected()) {
				for (const queue of this.queues) {
					try {
						await this.assertQueue(queue)
						logger.info(`重新声明队列成功: ${queue}`)
					} catch (error) {
						logger.error(`重新声明队列失败: ${queue}`, error)
					}
				}
			}
		} catch (error) {
			logger.error(`第 ${this.currentRetryCount} 次重连失败:`, error)
			// 重连失败，安排下一次重连
			this.scheduleReconnect()
		}
	}

	/**
	 * 计算下一次重连间隔（指数退避算法）
	 */
	private calculateNextReconnectInterval(): number {
		// 基础间隔 * 增长因子^重试次数，但不超过最大间隔
		const interval = Math.min(
			this.initialReconnectInterval * Math.pow(this.reconnectMultiplier, this.currentRetryCount),
			this.maxReconnectInterval
		)

		// 添加一些随机抖动，避免多个客户端同时重连
		return Math.floor(interval * (0.8 + Math.random() * 0.4))
	}

	/**
	 * 发送Webhook通知
	 */
	private async sendWebhookNotification(title: string, message: string, isCritical: boolean) {
		if (!this.enableWebhook) return

		try {
			const webhook = new WebHook()
			webhook.title(title, isCritical ? Colors.Error : Colors.Warning)
				.kv("MQ服务器", this.url)
				.kv("重试次数", `${this.currentRetryCount}/${this.maxRetries || '无限'}`)
				.kv("错误信息", message, isCritical ? Colors.Error : Colors.Warning)
				.timestamp()

			await webhook.send(this.webhookUrl)
			logger.info("已发送MQ连接问题的Webhook通知")
		} catch (error) {
			logger.error("发送Webhook通知失败:", error)
		}
	}

	/**
	 * 清理资源
	 */
	private async cleanup(complete: boolean) {
		// 停止心跳检测
		this.stopHeartbeatCheck()

		// 清除重连定时器
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer)
			this.reconnectTimer = null
		}

		// 清除连接Promise
		if (complete) {
			this.connectingPromise = null
		}

		// 关闭通道
		if (this.channel) {
			try {
				await this.channel.close()
			} catch (error) {
				// 忽略关闭错误
			}
			this.channel = null
		}

		// 关闭连接
		if (this.connection) {
			try {
				await this.connection.close()
			} catch (error) {
				// 忽略关闭错误
			}
			this.connection = null
		}

		// 如果是完全清理，则清空队列集合
		if (complete) {
			this.queues.clear()
		}
	}

	/**
	 * 发送消息到指定队列
	 * @param param0 包含队列名称和消息内容的对象
	 * @throws 如果客户端未连接，将抛出错误
	 */
	async sendToQueue({ queueName, message }: AMQPMessageQueue): Promise<void>
	/**
	 * 发送消息到指定队列
	 * @param queueName 队列名称
	 * @param message 消息内容
	 * @throws 如果客户端未连接，将抛出错误
	 */
	async sendToQueue(queueName: string, message: any): Promise<void>
	async sendToQueue(
		queueNameOrObj: string | AMQPMessageQueue,
		messageParam?: any,
	): Promise<void> {
		let queueName: string
		let message: any

		if (typeof queueNameOrObj === "string") {
			queueName = queueNameOrObj
			message = messageParam
		} else {
			queueName = queueNameOrObj.queueName
			message = queueNameOrObj.message
		}

		if (!this.channel) {
			throw new Error("MQ客户端未连接")
		}
		if (!this.queues.has(queueName)) {
			await this.assertQueue(queueName)
		}
		this.channel.sendToQueue(queueName, Buffer.from(JSON.stringify(message)))
	}

	/**
	 * 声明队列
	 */
	async assertQueue(queueName: string) {
		if (!this.channel) {
			throw new Error("MQ客户端未连接")
		}
		await this.channel.assertQueue(queueName, { durable: true })
		this.queues.add(queueName)
	}

	/**
	 * 确认消息已被处理
	 * @param msg 要确认的消息
	 * @throws 如果客户端未连接，将抛出错误
	 */
	async ackMessage(msg: amqp.Message) {
		if (this.channel) {
			this.channel.ack(msg)
		} else {
			throw new Error("MQ客户端未连接")
		}
	}

	/**
	 * 拒绝消息
	 * @param msg 要拒绝的消息
	 * @param requeue 是否将消息重新排队
	 * @throws 如果客户端未连接，将抛出错误
	 */
	async nackMessage(msg: amqp.Message, requeue = false) {
		if (this.channel) {
			this.channel.nack(msg, false, requeue)
		} else {
			throw new Error("MQ客户端未连接")
		}
	}

	/**
	 * 关闭连接
	 */
	async close() {
		await this.cleanup(true)
		this.connectionState = ConnectionState.DISCONNECTED
		logger.info("MQ客户端已关闭")
	}

	/**
	 * 检查是否已连接
	 */
	isConnected(): boolean {
		return this.channel !== null && this.connection !== null
	}

	/**
	 * 获取通道
	 */
	async getChannel(): Promise<amqp.Channel | amqp.ConfirmChannel | null> {
		// 如果正在重连中，返回null而不是尝试连接
		if (this.connectionState === ConnectionState.RECONNECTING) {
			return null
		}

		if (!this.isConnected()) {
			// 如果没有连接且不在重连中，尝试连接
			if (this.connectionState === ConnectionState.DISCONNECTED) {
				await this.connect()
			}
		}
		return this.channel
	}

	/**
	 * 检查是否是确认通道
	 */
	isConfirmChannel(): boolean {
		return this.channelOptions.confirmMode === true
	}

	/**
	 * 获取当前连接状态
	 */
	getConnectionState(): string {
		return this.connectionState
	}

	/**
	 * 获取当前重试次数
	 */
	getCurrentRetryCount(): number {
		return this.currentRetryCount
	}

	/**
	 * 重置重连状态
	 */
	resetReconnectState() {
		this.currentRetryCount = 0
		this.webhookSent = false
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer)
			this.reconnectTimer = null
		}
	}
}

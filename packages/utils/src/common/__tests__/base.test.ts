import { describe, expect, it } from "vitest"
import { safeRepairParseJson } from "../base"
import { jsonrepair } from "jsonrepair"

describe("repairJsonFromMarkdown", () => {
	describe("处理 markdown 代码块格式的 JSON", () => {
		it("应该正确解析包含 markdown 代码块的 JSON 数据", () => {
			const markdownJson = `\`\`\`json
[
  {
    "code": "hypercasual_games",
    "icon": "gamepad2",
    "name": "하이퍼 캐주얼 게임",
    "slug": "/c/hypercasual-games",
    "locale": "ko",
    "metadata": {
      "title": "하이퍼 캐주얼 게임",
      "description": "최고의 무료 하이퍼 캐주얼 게임을 즐겨보세요! 간단한 탭/스 와이프 방식, 중독성 있는 게임 플레이, 즉각적인 재미! 다운로드 없이 지금 2025년 인기 게임을 플레이하세요!"
    },
    "sortOrder": 12
  },
  {
    "code": "idle_clicker_games",
    "name": "방치형 게임 / 클리커 게임",
    "slug": "/c/idle-games-clicker-games",
    "locale": "ko",
    "sortOrder": 11
  },
  {
    "code": "merge_games",
    "name": "병합 게임",
    "slug": "/c/merge-games",
    "locale": "ko",
    "sortOrder": 10
  },
  {
    "code": "match_3_games",
    "name": "매치 3 게임",
    "slug": "/c/match-3-games",
    "locale": "ko",
    "sortOrder": 9
  },
  {
    "code": "hidden_object_games",
    "name": "숨은 그림 찾기 게임",
    "slug": "/c/hidden-object-games",
    "locale": "ko",
    "sortOrder": 8
  },
  {
    "code": "time_management_games",
    "name": "시간 관리 게임",
    "slug": "/c/time-management-games",
    "locale": "ko",
    "sortOrder": 7
  },
  {
    "code": "mini_games_compilations",
    "name": "미니 게임 모음",
    "slug": "/c/mini-games-compilations",
    "locale": "ko",
    "sortOrder": 6
  },
  {
    "code": "quick_reaction_games",
    "name": "순발력 게임",
    "slug": "/c/quick-reaction-games",
    "locale": "ko",
    "sortOrder": 5
  },
  {
    "code": "drawing_coloring_games",
    "name": "그리기 & 색칠 게임",
    "slug": "/c/drawing-coloring-games",
    "locale": "ko",
    "sortOrder": 4
  },
  {
    "code": "dress_up_games",
    "name": "옷 입히기 게임",
    "slug": "/c/dress-up-games",
    "locale": "ko",
    "sortOrder": 3
  },
  {
    "code": "pet_simulation_casual",
    "name": "반려동물 시뮬레이션 (캐주얼)",
    "slug": "/c/pet-simulation-casual",
    "locale": "ko",
    "sortOrder": 2
  },
  {
    "code": "social_simulation_casual",
    "name": "소셜 시뮬레이션 (캐주얼)",
    "slug": "/c/social-simulation-casual",
    "locale": "ko",
    "sortOrder": 1
  }
]
\`\`\``

			const result = safeRepairParseJson(markdownJson)

			expect(Array.isArray(result)).toBe(true)
			expect(result).toHaveLength(12)

			// 验证第一个对象的结构
			const firstItem = result[0]
			expect(firstItem.code).toBe("hypercasual_games")
			expect(firstItem.icon).toBe("gamepad2")
			expect(firstItem.name).toBe("하이퍼 캐주얼 게임")
			expect(firstItem.slug).toBe("/c/hypercasual-games")
			expect(firstItem.locale).toBe("ko")
			expect(firstItem.sortOrder).toBe(12)
			expect(firstItem.metadata).toEqual({
				title: "하이퍼 캐주얼 게임",
				description: "최고의 무료 하이퍼 캐주얼 게임을 즐겨보세요! 간단한 탭/스 와이프 방식, 중독성 있는 게임 플레이, 즉각적인 재미! 다운로드 없이 지금 2025년 인기 게임을 플레이하세요!"
			})

			// 验证最后一个对象
			const lastItem = result[result.length - 1]
			expect(lastItem.code).toBe("social_simulation_casual")
			expect(lastItem.name).toBe("소셜 시뮬레이션 (캐주얼)")
			expect(lastItem.sortOrder).toBe(1)
		})

		it("应该处理不完整的 markdown 代码块格式", () => {
			const incompleteMarkdown = `\`\`\`json
{
  "name": "test",
  "value": 123
}
\`\`\``

			const result = JSON.parse(jsonrepair(incompleteMarkdown))

			expect(result).toEqual({
				name: "test",
				value: 123
			})
		})

		it("应该处理包含额外空白的 markdown 代码块", () => {
			const markdownWithSpaces = `\`\`\`json

{
  "key": "value"
}

\`\`\``

			const result = safeRepairParseJson(markdownWithSpaces)

			expect(result).toEqual({
				key: "value"
			})
		})
	})

	describe("处理普通 JSON 字符串", () => {
		it("应该正确解析普通的 JSON 字符串", () => {
			const jsonString = '{"name": "test", "value": 123}'

			const result = safeRepairParseJson(jsonString)

			expect(result).toEqual({
				name: "test",
				value: 123
			})
		})

		it("应该修复格式错误的 JSON", () => {
			const malformedJson = '{name: "test", value: 123,}'

			const result = safeRepairParseJson(malformedJson)

			expect(result).toEqual({
				name: "test",
				value: 123
			})
		})

		it("应该处理包含韩文字符的 JSON", () => {
			const koreanJson = '{"name": "하이퍼 캐주얼 게임", "description": "최고의 무료 게임"}'

			const result = safeRepairParseJson(koreanJson)

			expect(result).toEqual({
				name: "하이퍼 캐주얼 게임",
				description: "최고의 무료 게임"
			})
		})
	})

	describe("错误处理", () => {
		it("应该在无法修复的 JSON 时抛出错误", () => {
			// 使用一个包含无法修复的语法错误的 JSON
			const invalidJson = '{{{{'

			expect(() => {
				safeRepairParseJson(invalidJson)
			}).toThrow("Failed to repair JSON")
		})

		it("应该在空字符串时抛出错误", () => {
			expect(() => {
				safeRepairParseJson("")
			}).toThrow("Failed to repair JSON")
		})

		it("应该处理只有 markdown 标记但没有内容的情况", () => {
			const emptyMarkdown = "```json\n\n```"

			expect(() => {
				safeRepairParseJson(emptyMarkdown)
			}).toThrow("Failed to repair JSON")
		})
	})

	describe("边缘情况", () => {
		it("应该处理嵌套的 markdown 代码块", () => {
			const nestedMarkdown = `\`\`\`json
{
  "content": "这里有一个代码块: \\\`\\\`\\\`json\\n{\\\"nested\\\": true}\\n\\\`\\\`\\\`",
  "value": 42
}
\`\`\``

			const result = safeRepairParseJson(nestedMarkdown)

			expect(result.content).toContain("```json")
			expect(result.value).toBe(42)
		})

		it("应该处理包含特殊字符的 JSON", () => {
			const specialCharsJson = `\`\`\`json
{
  "symbols": "!@#$%^&*()_+-=[]{}|;':,.<>?",
  "unicode": "🎮🎯🎲",
  "quotes": "He said \\"Hello\\""
}
\`\`\``

			const result = safeRepairParseJson(specialCharsJson)

			expect(result.symbols).toBe("!@#$%^&*()_+-=[]{}|;':,.<>?")
			expect(result.unicode).toBe("🎮🎯🎲")
			expect(result.quotes).toBe('He said "Hello"')
		})

		it("应该处理大型数组数据", () => {
			const items = Array.from({ length: 100 }, (_, i) => `  {"id": ${i}, "name": "item_${i}"}`)
			const largeArray = `\`\`\`json
[
${items.join(",\n")}
]
\`\`\``

			const result = safeRepairParseJson(largeArray)

			expect(Array.isArray(result)).toBe(true)
			expect(result).toHaveLength(100)
			expect(result[0]).toEqual({ id: 0, name: "item_0" })
			expect(result[99]).toEqual({ id: 99, name: "item_99" })
		})
	})
})

// 生成一个随机的6位字符串（包含小写字母和数字）
export const generateRandomString = (length = 6) => {
	const chars = "abcdefghijklmnopqrstuvwxyz0123456789"
	let result = ""
	for (let i = 0; i < length; i++) {
		result += chars.charAt(Math.floor(Math.random() * chars.length))
	}
	return result
}

// 生成slug
export const generateSlug = (name: string): string => {
	if (!name) return ""
	return name
		.toLowerCase()
		.replace(/[^a-z0-9]+/g, "-")
		.replace(/^-+|-+$/g, "")
}

export function safeJsonParse(value: any, defaultValue: any = null) {
	try {
		if (typeof value === "string") {
			return JSON.parse(value)
		}
		return value ?? defaultValue
	} catch (e) {
		return defaultValue
	}
}

export function safeJsonStringify(value: any, defaultValue: any = null) {
	try {
		return JSON.stringify(value)
	} catch (e) {
		return defaultValue
	}
}



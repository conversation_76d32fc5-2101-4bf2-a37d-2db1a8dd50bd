import { getLogger } from "@repo/logger"
import <PERSON> from "jenkins"

const logger = getLogger("JenkinsService")
export const DEPLOYMENT_TYPE = {
	PREVIEW: "PREVIEW",
	PRODUCTION: "PRODUCTION",
}
// 获取Jenkins构建任务名称
export const buildJobName =
	process.env.JENKINS_BUILD_JOB_NAME || "qizhilu-deploy"
// 获取Jenkins预览构建任务名称
export const previewJobName =
	process.env.JENKINS_PREVIEW_JOB_NAME || "qizhilu-deploy-preview"

/**
 * Jenkins API服务
 * 使用jenkins npm包与Jenkins交互，触发构建和获取构建状态
 */
export class JenkinsService {
	private jenkins: any
	private callbackUrl: string
	private callbackToken: string
	private buildJobName: string
	private previewJobName: string
	private baseUrl: string
	private webApiUrl: string
	constructor() {
		this.baseUrl = process.env.JENKINS_API_URL || ""
		const username = process.env.JENKINS_USERNAME || ""
		const apiToken = process.env.JENKINS_API_TOKEN || ""
		this.callbackUrl = `${process.env.JENKINS_CALLBACK_BASE_URL}/api/jenkins/callback`
		this.webApiUrl = `${process.env.JENKINS_CALLBACK_BASE_URL}/api/gateway`
		this.callbackToken = process.env.JENKINS_CALLBACK_TOKEN || ""
		this.buildJobName = buildJobName
		this.previewJobName = previewJobName

		if (!this.baseUrl || !username || !apiToken) {
			logger.warn("Jenkins配置不完整，请检查环境变量")
		}

		// 初始化Jenkins客户端
		this.jenkins = new Jenkins({
			baseUrl: this.baseUrl,
			crumbIssuer: true,
			headers: {
				Authorization: `Basic ${Buffer.from(`${username}:${apiToken}`).toString("base64")}`,
			},
		})
	}

	/**
	 * 触发Jenkins构建
	 * @param projectId 项目ID
	 * @param deploymentId 部署记录ID
	 * @param parameters 构建参数
	 * @param customJobName 可选的自定义任务名称
	 * @returns 构建信息 {queueItemNumber, buildNumber?, inQueue, queueReason?}
	 */
	async triggerBuild(
		projectId: string,
		deploymentId: string,
		parameters?: Record<string, any>,
		customJobName?: string,
	): Promise<{
		queueItemNumber: number
		buildNumber?: number
		inQueue: boolean
		queueReason?: string
	}> {
		try {
			// 构建参数
			const params = {
				...parameters,
				project_id: projectId,
				deployment_id: deploymentId,
				callback_url: this.callbackUrl,
				callback_token: this.callbackToken,
				web_api_url: this.webApiUrl,
			}
			const jobName = customJobName || this.buildJobName
			logger.info(
				`触发Jenkins构建，任务: ${jobName}，构建参数: ${JSON.stringify(params)}`,
			)

			// 使用jenkins npm包触发构建
			const queueItemNumber = await this.jenkins.job.build({
				name: jobName,
				parameters: params,
			})

			logger.info(`Jenkins构建已触发，队列项编号: ${queueItemNumber}`)

			try {
				// 尝试等待队列项转换为构建，但不强制等待
				// 如果在短时间内转换成功，返回构建编号
				const buildNumber = await this.tryGetBuildNumber(queueItemNumber)
				if (buildNumber) {
					logger.info(`Jenkins构建已开始，构建编号: ${buildNumber}`)
					return { queueItemNumber, buildNumber, inQueue: false }
				}
			} catch (waitError) {
				// 忽略等待错误，继续处理
				logger.debug(`等待构建开始超时，将返回队列状态: ${waitError}`)
			}

			// 如果没有立即转换为构建，获取队列状态
			const queueStatus = await this.checkQueueItemStatus(queueItemNumber)
			return {
				queueItemNumber,
				inQueue: queueStatus.inQueue,
				queueReason: queueStatus.reason,
			}
		} catch (error) {
			logger.error("触发Jenkins构建失败:", error)
			throw new Error(
				`触发Jenkins构建失败: ${error instanceof Error ? error.message : "未知错误"}`,
			)
		}
	}

	/**
	 * 尝试获取构建编号，但不强制等待
	 * 只等待很短的时间，如果没有转换为构建，返回undefined
	 * @param queueItemNumber 队列项编号
	 * @returns 构建编号或undefined
	 */
	async tryGetBuildNumber(
		queueItemNumber: number,
	): Promise<number | undefined> {
		const maxAttempts = 5 // 只尝试几次
		const delayMs = 1000 // 每次等待2秒
		let attempts = 0

		while (attempts < maxAttempts) {
			try {
				// 获取队列项信息
				const queueItem = await this.jenkins.queue.item(queueItemNumber)

				// 检查是否已经转换为构建
				if (queueItem.executable) {
					return queueItem.executable.number
				}

				// 如果被取消，抛出错误
				if (queueItem.cancelled) {
					throw new Error("Jenkins构建已被取消")
				}

				// 等待后重试
				await new Promise((resolve) => setTimeout(resolve, delayMs))
				attempts++
			} catch (error) {
				logger.debug(
					`尝试获取构建编号失败 (尝试 ${attempts}/${maxAttempts}):`,
					error,
				)
				// 等待后重试
				await new Promise((resolve) => setTimeout(resolve, delayMs))
				attempts++
			}
		}

		// 如果没有转换为构建，返回undefined
		return undefined
	}

	/**
	 * 等待队列项转换为构建
	 * @param queueItemNumber 队列项编号
	 * @returns 构建编号
	 */
	private async waitForQueueItemToBuild(
		queueItemNumber: number,
	): Promise<number> {
		const maxAttempts = 30
		const delayMs = 2000
		let attempts = 0

		while (attempts < maxAttempts) {
			try {
				// 使用jenkins npm包获取队列项信息
				const queueItem = await this.jenkins.queue.item(queueItemNumber)

				// 检查是否已经转换为构建
				if (queueItem.executable) {
					return queueItem.executable.number
				}

				// 如果被取消
				if (queueItem.cancelled) {
					throw new Error("Jenkins构建已被取消")
				}

				// 等待后重试
				await new Promise((resolve) => setTimeout(resolve, delayMs))
				attempts++
			} catch (error) {
				if (attempts >= maxAttempts) {
					logger.error("等待Jenkins构建超时:", error)
					throw new Error("等待Jenkins构建超时")
				}

				// 等待后重试
				await new Promise((resolve) => setTimeout(resolve, delayMs))
				attempts++
			}
		}

		throw new Error("等待Jenkins构建超时")
	}

	/**
	 * 获取构建状态
	 * @param buildNumber 构建编号
	 * @param customJobName 可选的自定义任务名称
	 * @returns 构建状态
	 */
	async getBuildStatus(
		buildNumber: number,
		customJobName?: string,
	): Promise<{
		status: "SUCCESS" | "FAILURE" | "ABORTED" | "IN_PROGRESS" | "UNKNOWN"
		url: string
	}> {
		try {
			const jobName = customJobName || this.buildJobName
			// 使用jenkins npm包获取构建信息
			const buildInfo = await this.jenkins.build.get(jobName, buildNumber)
			const buildUrl = buildInfo.url

			if (buildInfo.building) {
				return { status: "IN_PROGRESS", url: buildUrl }
			}

			if (buildInfo.result === "SUCCESS") {
				return { status: "SUCCESS", url: buildUrl }
			} else if (buildInfo.result === "FAILURE") {
				return { status: "FAILURE", url: buildUrl }
			} else if (buildInfo.result === "ABORTED") {
				return { status: "ABORTED", url: buildUrl }
			}

			return { status: "UNKNOWN", url: buildUrl }
		} catch (error) {
			logger.error("获取Jenkins构建状态失败:", error)
			throw new Error(
				`获取Jenkins构建状态失败: ${error instanceof Error ? error.message : "未知错误"}`,
			)
		}
	}

	/**
	 * 获取构建日志
	 * @param buildNumber 构建编号
	 * @param customJobName 可选的自定义任务名称
	 * @returns 构建日志
	 */
	async getBuildLog(
		buildNumber: number,
		customJobName?: string,
	): Promise<string> {
		try {
			const jobName = customJobName || this.buildJobName
			// 使用jenkins npm包获取构建日志
			return await this.jenkins.build.log(jobName, buildNumber)
		} catch (error) {
			logger.error("获取Jenkins构建日志失败:", error)
			throw new Error(
				`获取Jenkins构建日志失败: ${error instanceof Error ? error.message : "未知错误"}`,
			)
		}
	}

	/**
	 * 获取构建控制台输出URL
	 * @param buildNumber 构建编号
	 * @param customJobName 可选的自定义任务名称
	 * @returns 构建控制台输出URL
	 */
	getBuildConsoleUrl(buildNumber: number, customJobName?: string): string {
		const jobName = customJobName || this.buildJobName
		return `${this.baseUrl}/job/${jobName}/${buildNumber}/console`
	}

	/**
	 * 停止Jenkins构建
	 * @param buildNumber 构建编号
	 * @param customJobName 可选的自定义任务名称
	 * @returns 是否成功停止
	 */
	async stopBuild(
		buildNumber: number,
		customJobName?: string,
	): Promise<boolean> {
		try {
			const jobName = customJobName || this.buildJobName
			logger.info(`停止Jenkins构建，构建编号: ${buildNumber}, 任务: ${jobName}`)

			// 使用jenkins npm包停止构建
			await this.jenkins.build.stop(jobName, buildNumber)
			logger.info(`Jenkins构建已停止，构建编号: ${buildNumber}`)
			return true
		} catch (error) {
			logger.error("停止Jenkins构建失败:", error)
			throw new Error(
				`停止Jenkins构建失败: ${error instanceof Error ? error.message : "未知错误"}`,
			)
		}
	}

	/**
	 * 检查队列项状态
	 * @param queueItemNumber 队列项编号
	 * @returns 队列项状态 {inQueue: boolean, reason: string}
	 */
	async checkQueueItemStatus(
		queueItemNumber: number,
	): Promise<{ inQueue: boolean; reason: string }> {
		try {
			// 获取队列项信息
			const queueItem = await this.jenkins.queue.item(queueItemNumber)

			// 如果已经转换为构建，则不在队列中
			if (queueItem.executable) {
				return { inQueue: false, reason: "已开始构建" }
			}

			// 如果被取消
			if (queueItem.cancelled) {
				return { inQueue: false, reason: "构建已被取消" }
			}

			// 检查是否被阻塞
			if (queueItem.blocked) {
				return {
					inQueue: true,
					reason: `队列中被阻塞: ${queueItem.why || "未知原因"}`,
				}
			}

			// 检查是否在等待
			if (queueItem.stuck) {
				return {
					inQueue: true,
					reason: `队列中被卡住: ${queueItem.why || "未知原因"}`,
				}
			}

			// 默认情况下，如果没有executable，就认为还在队列中
			return {
				inQueue: true,
				reason: queueItem.why || "在Jenkins队列中等待",
			}
		} catch (error) {
			logger.error("检查Jenkins队列项状态失败:", error)
			// 如果出错，假设不在队列中
			return { inQueue: false, reason: "无法获取队列状态" }
		}
	}
}

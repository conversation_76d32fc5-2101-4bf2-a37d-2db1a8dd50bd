import { z } from "zod"
import { generateObject, NoObjectGeneratedError, generateText } from "ai"
import { getLogger } from "@repo/logger"
import { chatModel, MODEL_OPENROUTER_GEMINI_2_0 } from "@repo/utils/server"
import { pick } from "radash"

const logger = getLogger("TranslationService")

// 翻译错误类
export class TranslationError extends Error {
	constructor(
		message: string,
		public readonly cause?: unknown,
		public readonly sourceText?: string,
		public readonly targetLanguage?: string,
	) {
		super(message)
		this.name = "TranslationError"
	}
}

// 翻译服务配置
export interface TranslationServiceConfig {
	model: ReturnType<typeof chatModel>
	timeout?: number
}

// 基本翻译选项
export interface TranslationOptions {
	sourceLanguage: string
	targetLanguage: string
	preserveFormatting?: boolean
	timeout?: number
	customPrompt?: string
}

// 结构化对象翻译选项
export interface ObjectTranslationOptions extends TranslationOptions {
	translateKeys?: boolean
	fieldsToTranslate?: string[]
	customObjectPrompt?: string
	isArray?: boolean // 指示是否生成数组输出
}

// 多语言翻译选项
export interface MultiLanguageTranslationOptions {
	sourceLanguage: string
	targetLanguages: string[]
	preserveFormatting?: boolean
	timeout?: number
	customPrompt?: string
}

// 翻译结果
export interface TranslationResult {
	translatedText: string
	detectedLanguage?: string
}

// 多语言翻译结果
export interface MultiLanguageTranslationResult {
	[language: string]: TranslationResult
}

// 默认提示词模板
export const DEFAULT_PROMPTS = {
	// 文本翻译提示词
	text: (
		text: string,
		sourceLanguage: string,
		targetLanguage: string,
		preserveFormatting: boolean,
	) => {
		let prompt = `Translate the following ${sourceLanguage} text into ${targetLanguage}. This is for a game localization project, so maintain the tone, style, and cultural nuances appropriate for gaming content:

${text}

Instructions:
1. Ensure the translation is natural and idiomatic in the target language
2. Adapt cultural references appropriately for the target audience
3. Maintain gaming terminology consistency
4. Preserve the emotional impact and tone of the original text`

		if (preserveFormatting) {
			prompt += `
5. Preserve all formatting, including line breaks, spacing, and punctuation`
		}

		return prompt
	},

	// 对象翻译提示词
	object: (
		object: Record<string, any>,
		sourceLanguage: string,
		targetLanguage: string,
		translateKeys: boolean,
		fieldsToTranslate?: string[],
		isArray?: boolean,
	) => {
		// 如果指定了要翻译的字段，只提取这些字段用于翻译
		// 使用 radash 的 pick 函数来选择指定的字段
		const objectToTranslate =
			fieldsToTranslate && fieldsToTranslate.length > 0
				? pick(object, fieldsToTranslate)
				: object

		const objectStr = JSON.stringify(objectToTranslate, null, 2)
		const dataType = isArray ? "JSON array of objects" : "JSON object"

		let prompt = `Translate the following ${dataType} from ${sourceLanguage} to ${targetLanguage}. This is for a game localization project, so maintain gaming terminology and cultural nuances:

${objectStr}

Instructions:
1. Ensure translations are natural and idiomatic in the target language
2. Adapt gaming terminology appropriately for the target audience
3. Maintain the exact JSON structure in your response
4. Ensure the output is valid JSON`

		if (isArray) {
			prompt += `
5. Keep the same number of elements in the array
6. Preserve the structure of each object in the array`
		} else if (translateKeys) {
			prompt += `
5. Translate both keys and values in the JSON object`
		} else {
			prompt += `
5. Only translate values, NOT keys`
		}

		return prompt
	},
}

/**
 * 创建翻译服务配置
 * @param model 使用的模型，由chatModel()函数返回
 * @param timeout 超时时间（毫秒），默认为30000ms (30秒)
 */
export function createTranslationService(
	model: ReturnType<typeof chatModel>,
	timeout = 30000,
): TranslationServiceConfig {
	return { model, timeout }
}

// 默认翻译服务配置
const defaultConfig = createTranslationService(chatModel(MODEL_OPENROUTER_GEMINI_2_0))

/**
 * 翻译文本
 * @param text 要翻译的文本
 * @param options 翻译选项
 * @param config 翻译服务配置，默认使用MODEL_DOUBAO_PRO_32K模型
 * @returns 翻译结果
 * @throws {TranslationError} 翻译失败时抛出
 */
export async function translateText(
	text: string,
	options: TranslationOptions,
	config: TranslationServiceConfig = defaultConfig,
): Promise<string> {
	try {
		if (!text || text.trim() === "") {
			return ""
		}

		const {
			sourceLanguage,
			targetLanguage,
			preserveFormatting = true,
			timeout = config.timeout || 30000,
			customPrompt,
		} = options

		// 构建提示词 - 优先使用自定义提示词
		const prompt =
			customPrompt ||
			DEFAULT_PROMPTS.text(
				text,
				sourceLanguage,
				targetLanguage,
				preserveFormatting,
			)

		// 设置 AbortController 用于超时控制
		const controller = new AbortController()
		const timeoutId = setTimeout(() => controller.abort(), timeout)

		try {
			const { text } = await generateText({
				model: config.model,
				prompt,
				abortSignal: controller.signal,
			})

			return text
		} catch (error) {
			if (error instanceof NoObjectGeneratedError) {
				throw new TranslationError(
					`Translation generation failed: ${error.message}`,
					error,
					text,
					targetLanguage,
				)
			} else if (error instanceof Error && error.name === "AbortError") {
				throw new TranslationError(
					`Translation timeout: exceeded ${timeout}ms`,
					error,
					text,
					targetLanguage,
				)
			}
			throw error
		} finally {
			clearTimeout(timeoutId)
		}
	} catch (error) {
		if (error instanceof TranslationError) {
			throw error
		}
		throw new TranslationError(
			`Translation failed: ${error instanceof Error ? error.message : String(error)}`,
			error,
			text,
			options.targetLanguage,
		)
	}
}

/**
 * 翻译结构化对象
 * @param object 要翻译的对象
 * @param schema 对象的结构定义
 * @param options 翻译选项
 * @param config 翻译服务配置，默认使用MODEL_DOUBAO_PRO_32K模型
 * @returns 翻译后的对象
 * @throws {TranslationError} 翻译失败时抛出
 */
export async function translateObject<T extends Record<string, any>>(
	object: T,
	schema: z.ZodType<T>,
	options: ObjectTranslationOptions,
	config?: TranslationServiceConfig,
): Promise<T>
export async function translateObject<T extends Record<string, any>>(
	object: T,
	schema: z.ZodType<T>,
	options: ObjectTranslationOptions,
	config?: TranslationServiceConfig,
): Promise<T[]>
export async function translateObject<T extends Record<string, any>>(
	object: T,
	schema: z.ZodType<T>,
	options: ObjectTranslationOptions,
	config: TranslationServiceConfig = defaultConfig,
): Promise<T | T[]> {
	try {
		if (!object || Object.keys(object).length === 0) {
			return object
		}

		const {
			sourceLanguage,
			targetLanguage,
			translateKeys = false,
			timeout = config.timeout || 30000,
			customObjectPrompt,
			isArray = false, // 默认为对象模式
		} = options

		// 构建提示词 - 优先使用自定义提示词
		const prompt =
			customObjectPrompt ||
			DEFAULT_PROMPTS.object(
				object, // 传递需要翻译的对象
				sourceLanguage,
				targetLanguage,
				translateKeys,
				options.fieldsToTranslate, // 传递要翻译的字段
				isArray, // 是否为数组模式
			)

		// 设置 AbortController 用于超时控制
		const controller = new AbortController()
		const timeoutId = setTimeout(() => controller.abort(), timeout)

		try {
			// 根据 isArray 参数决定是否使用数组输出模式
			if (isArray) {
				// 数组模式
				const result = await generateObject({
					model: config.model,
					schema: schema,
					output: "array",
					prompt,
					abortSignal: controller.signal,
				})
				return result.object as T[]
			} else {
				// 对象模式
				const result = await generateObject({
					model: config.model,
					schema: schema,
					prompt,
					abortSignal: controller.signal,
				})
				return result.object as T
			}
		} catch (error) {
			if (error instanceof NoObjectGeneratedError) {
				throw new TranslationError(
					`Object translation generation failed: ${error.message}`,
					error,
					JSON.stringify(object),
					targetLanguage,
				)
			} else if (error instanceof Error && error.name === "AbortError") {
				throw new TranslationError(
					`Object translation timeout: exceeded ${timeout}ms`,
					error,
					JSON.stringify(object),
					targetLanguage,
				)
			}
			throw error
		} finally {
			clearTimeout(timeoutId)
		}
	} catch (error) {
		if (error instanceof TranslationError) {
			throw error
		}
		throw new TranslationError(
			`Object translation failed: ${error instanceof Error ? error.message : String(error)}`,
			error,
			JSON.stringify(object),
			options.targetLanguage,
		)
	}
}

/**
 * 并行翻译文本到多种语言
 * @param text 要翻译的文本
 * @param options 多语言翻译选项
 * @param config 翻译服务配置，默认使用MODEL_DOUBAO_PRO_32K模型
 * @returns 包含所有目标语言翻译结果的对象
 * @throws {TranslationError} 翻译失败时抛出
 */
export async function translateTextToMultipleLanguages(
	text: string,
	options: MultiLanguageTranslationOptions,
	config: TranslationServiceConfig = defaultConfig,
): Promise<MultiLanguageTranslationResult> {
	try {
		if (!text || text.trim() === "") {
			return options.targetLanguages.reduce((acc, lang) => {
				acc[lang] = { translatedText: "" }
				return acc
			}, {} as MultiLanguageTranslationResult)
		}

		const {
			sourceLanguage,
			targetLanguages,
			preserveFormatting = true,
			timeout = config.timeout || 30000,
			customPrompt,
		} = options

		// 并行执行所有翻译任务
		const translationPromises = targetLanguages.map(async (targetLanguage) => {
			try {
				const result = await translateText(
					text,
					{
						sourceLanguage,
						targetLanguage,
						preserveFormatting,
						timeout,
						customPrompt,
					},
					config,
				)
				return { language: targetLanguage, result }
			} catch (error) {
				logger.error(`Translation to ${targetLanguage} failed:`, error)
				return {
					language: targetLanguage,
					result: { translatedText: text, error: String(error) },
				}
			}
		})

		const results = await Promise.all(translationPromises)

		// 将结果转换为 { [language]: result } 格式
		return results.reduce((acc, { language, result }) => {
			//@ts-ignore
			acc[language] = result
			return acc
		}, {} as MultiLanguageTranslationResult)
	} catch (error) {
		throw new TranslationError(
			`Multi-language translation failed: ${error instanceof Error ? error.message : String(error)}`,
			error,
			text,
		)
	}
}
